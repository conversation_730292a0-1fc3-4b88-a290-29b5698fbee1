package com.hp.settle.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.ServletContextAware;

import javax.servlet.ServletContext;

/**
 * Project: boss-service-settle
 * Date : 2023/10/24 15:39
 * Author : hw
 * Description : 获取servletContext
 */
@Component
public class ServletContextConfig implements ServletContextAware {

    public ServletContext servletContext;

    @Autowired
    public ServletContextConfig(ServletContext servletContext) {
        this.servletContext = servletContext;
    }

    @Override
    public void setServletContext(ServletContext servletContext) {
        this.servletContext = servletContext;
    }
}
