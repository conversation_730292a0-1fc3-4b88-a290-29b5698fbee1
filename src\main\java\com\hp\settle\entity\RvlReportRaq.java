package com.hp.settle.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * Project: boss-service-settle
 * Date : 2023/10/16 16:01
 * Author : hw
 * Description : 报表树版本控制
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName(value = "RVL_REPORT_RAQ")
public class RvlReportRaq extends Model {
    private static final long sntalVersionUID = 1L;

    @TableId("R_ID")
    private BigDecimal rid;

    @TableField("R_KEY")
    private String rKey;

    @TableField("R_NAME")
    private String rName;

    @TableField("R_STATUS")
    private String rStatus;

    @TableField("R_RAQURL")
    private String rRaqurl;

    @TableField("START_PAYMENT")
    private String startPayment;

    @TableField("END_PAYMENT")
    private String endPayment;

    @TableField("USE_CACHE")
    private String useCache;
}
