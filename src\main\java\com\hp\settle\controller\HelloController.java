package com.hp.settle.controller;

import com.hp.settle.entity.RvlReportRaq;
import com.hp.settle.service.IRvlReportRaqService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Project: boss-service-settle
 * Date : 2023/10/8 17:28
 * Author : hw
 * Description :
 */
@RestController
@RequestMapping("/test")
@Slf4j
public class HelloController {

    @Autowired
    private IRvlReportRaqService rvlReportRaqService;

    @RequestMapping(value = "/test", method = RequestMethod.GET)
    @ResponseBody
    public List<RvlReportRaq> test(@RequestParam("rKey") String rKey) {
        System.out.println(rKey);
        return rvlReportRaqService.getRvlReportRaqUrl(rKey);
    }
}
