package com.hp.settle.controller;

import com.hp.settle.service.SettleReportService;
import com.hp.settle.vo.BaseRspsMsg;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

//http://10.248.50.224/absweb/crmSettle/v3/settle/SettleReportV3?bssnum=000admin&elemCode=settlementV3
@Controller
@Slf4j
@RequestMapping("/settle")
public class SettleController {

    @RequestMapping("/toJsp")
    public ModelAndView toJsp(String cx) {
        ModelAndView view = new ModelAndView();
        System.out.println(">>>>>>toJsp...");
        System.out.println("hnhds");
        view.addObject("name", "gwm");
        view.setViewName(cx);
        return view;
    }

    @Autowired
    private SettleReportService settleReportService;


        /**
         * 功能描述:获取报表树
         */
    @RequestMapping(value = "/SettleReport", method = RequestMethod.GET, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public BaseRspsMsg getZtree(@RequestParam("treeType") String treeType) {
        BaseRspsMsg baseRspsMsg ;
        try{
            //log.info("treeType:{}, staffNum:{}.", treeType, staffNum);
//            JwtPayload jwtPayload = (JwtPayload)request.getAttribute(JwtPayload.class.getName());
            //全中心越权访问问题，前端传的staffnum和token获取的要一致，不一致则认为权限有问题
//            staffNum =  RSAUtils.decryptBase64ByPrivateKey(staffNum);
           // log.info("客户信息:{}",staffNum);
//            log.info("-->queryJobSpecByJobspecIdJobTypeId,jwtPayload:{}",jwtPayload==null?null: JSON.toJSONString(jwtPayload));
//            if (jwtPayload==null || StringUtils.isBlank(jwtPayload.getName()) || StringUtils.isBlank(staffNum) || !jwtPayload.getName().equals(staffNum)) {
//                baseRspsMsg = BaseRspsMsg.build(BaseRspsMsg.BIZ_CODE_00001_FAILE, "鉴权失败。");
//                return baseRspsMsg;
//            }
            baseRspsMsg = BaseRspsMsg.ok(settleReportService.getMenuTree(treeType));
        } catch (Exception e){
            log.error("系统异常！", e);
            baseRspsMsg = BaseRspsMsg.fail("系统异常");
        }
        return baseRspsMsg;
    }


}
