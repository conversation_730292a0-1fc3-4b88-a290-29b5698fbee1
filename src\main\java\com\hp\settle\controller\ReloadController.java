package com.hp.settle.controller;

import com.hp.settle.service.ReloadService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@Slf4j
@RestController
@RequestMapping("/reload")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ReloadController {

    private final ReloadService reloadService;

    @PostMapping
    public void reload(HttpServletRequest request, HttpServletResponse response) {
        try {
            reloadService.reload(request, response);
        } catch (Exception e) {
            log.error("error:{}", e.getMessage(), e);
        }
    }

}
