package com.hp.settle.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;

@Data
@Slf4j
@EqualsAndHashCode
@JsonIgnoreProperties(ignoreUnknown = true)
public class AmTreeCheck {

    @JsonProperty(value = "TREE_CHECK_ID")
    private Integer treeCheckId;

    @JsonProperty(value = "TREE_NODE_ID")
    private Integer treeNodeId;

    @JsonProperty(value = "ACCT_MONTH")
    private Integer acctMonth;

    @JsonProperty(value = "STATE")
    private String state;

    @JsonProperty(value = "REMARK")
    private String remark;

    @JsonProperty(value = "CREATE_DATE")
    private Date creatDate;

    @JsonProperty(value = "STAFF_NUMBER")
    private String staffNumber;

}
