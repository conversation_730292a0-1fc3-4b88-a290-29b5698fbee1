package com.hp.settle.entity;

import lombok.Data;

/**
 * @Classname ConfSettleDictModel
 * @Description 暂估/销暂估单文件配置表
 * @Date 2022/12/21 14:44
 * <AUTHOR>
 * @Version 1.0.0
 */
@Data
public class ConfSettleDictModel {

    // 报表key
    private String rKey;

    // 报表文件名
    private String reportName;

    // 费项:0 非调账 1 调账
    private String adjustValue;

    // 税率
    private String taxrateValue;

    // 签约主体
    private String entityValue;

    // 归属省 1是 0 否
    private String isProvince;

    private String settleMonth;

    // 当前账期的上一个月
    private String beforeMonth;

    // 是否生产Excel 1是 0否
    private String excelFlag;

    // 是否生产PDF 1是 0否
    private String pdfFlag;

    // 报表类型 1暂估销暂估 2其他报表
    private String raqType;

}
