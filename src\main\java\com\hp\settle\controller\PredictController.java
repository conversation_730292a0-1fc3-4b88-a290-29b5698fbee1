package com.hp.settle.controller;

import com.hp.settle.api.R;
import com.hp.settle.service.PredictService;
import com.hp.settle.util.CommonUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.nio.charset.StandardCharsets;

@Slf4j
@RestController
@RequestMapping("/api")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class PredictController {

    private final PredictService predictService;

    // http://localhost:8081/reportJsp/Prediction.jsp?bssnum=000admin&elemCode=settlementV3
    @PostMapping("/prediction")
    public R getExports(HttpServletRequest request, HttpServletResponse response) {
        log.info("***Predict报表批量下载start***");
        try {
            CommonUtil.shareRequest(request);
            request.setCharacterEncoding(String.valueOf(StandardCharsets.UTF_8));
            response.setCharacterEncoding(String.valueOf(StandardCharsets.UTF_8));
            predictService.doFiles(request, response);
            log.info("***Predict报表批量下载end***");
            return R.ok();
        } catch (Exception e) {
            log.error("Predict报表批量下载error:{}", e.getMessage(), e);
            return R.error(e.getMessage());
        }finally {
            CommonUtil.remove();
        }
    }

    @GetMapping("/predictionDownload")
    public void downloadBatch(HttpServletRequest request, HttpServletResponse response) {
        try {
            request.setCharacterEncoding(String.valueOf(StandardCharsets.UTF_8));
            response.setCharacterEncoding(String.valueOf(StandardCharsets.UTF_8));
            predictService.downloadBatch(request, response);
            log.info("predictionDownload批量下载成功");
        } catch (Exception e) {
            log.error("predictionDownload批量下载失败error:{}", e.getMessage(), e);
        }
    }

}
