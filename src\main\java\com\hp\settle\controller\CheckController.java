package com.hp.settle.controller;

import com.hp.settle.service.ReportCheckService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * Project: boss-service-settle
 * Date : 2023/10/23 17:08
 * Author : hw
 * Description :点击查询报表或者下载Zip包时检查是否审核完成
 */
@RestController
@Slf4j
@RequestMapping("/report")
public class CheckController {

    @Autowired
    private ReportCheckService reportCheckService;

    @RequestMapping(value = "/isCheck", method = RequestMethod.GET)
    public String isCheck(HttpServletRequest request,
                          HttpServletResponse response){
            return reportCheckService.isCheck(request, response);
        }
}
