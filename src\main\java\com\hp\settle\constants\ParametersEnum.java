package com.hp.settle.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @ClassName: ParametersEnum 
 * @Description: 批量报表对应表KEY及下载文件名称及打包名称
 * @company HPE  
 * <AUTHOR>  
 * @date 2019-12-13 
 *  
 */
@Getter
@AllArgsConstructor
public enum ParametersEnum {
	/**  
	 * TAXRATE : 税率
	 * ENTITY  : 签约主体
	 * YYYYMM  : 结算账期
	 * INCOME  : 
	 * INOUT   : 结入结出
	 */ 
	STL_10001("10001", "Y", "BL", "政企分公司", "财务部"),
	STL_10002("10002", "Y", "AR", "政企分公司", "财务部"),
	STL_10015("10015", "Y", "", "政企分公司", "财务部"),
	STL_10074("10074", "N", "BL", "政企分公司", "财务部"),
	STL_10075("10075", "N", "BL", "政企分公司", "财务部"),
	STL_10007("10007", "Y", "BL", "财务公司", "财务公司"),
	STL_10008("10008", "Y", "AR", "财务公司", "财务公司"),
	STL_10009("10009", "Y", "BL", "财务公司", "财务公司"),
	STL_10010("10010", "Y", "AR", "财务公司", "财务公司"),
	STL_10021("10021", "Y", "BL", "财务公司", "财务公司"),
	STL_10022("10022", "Y", "AR", "财务公司", "财务公司"),
    STL_10161("10161", "Y", "BL", "财务公司", "财务公司"),
    STL_10168("10168", "Y", "BL", "财务公司", "财务公司"),
	STL_10019("10019", "Y", "", "财务公司", "财务公司"),
	STL_10020("10020", "Y", "", "财务公司", "财务公司"),
	STL_10027("10027", "Y", "", "政企分公司", "政企售后"), //20200513
	STL_10034("10034", "Y", "", "财务公司", "财务公司"),
	STL_10036("10036", "Y", "", "基地", "基地"),
	STL_10037("10037", "Y", "", "基地", "基地"),
	STL_10069("10069", "Y", "", "基地", "基地"),
	STL_10090("10090", "N", "BL", "财务公司", "财务公司"),
	STL_10091("10091", "N", "BL", "财务公司", "财务公司"),
	STL_10176("10176", "Y", "BL", "财务公司", "财务公司"),

	STL_10178("10178", "Y", "BL", "协同营销", "协同营销"),
	STL_10179("10179", "Y", "BL", "协同营销", "协同营销"),
	STL_10180("10180", "Y", "BL", "财务公司", "财务公司"),

	STL_10001_x("10001_x", "Y", "BL", "政企分公司", "财务部"),
	STL_10007_x("10007_x", "Y", "BL", "财务公司", "财务公司"),
	STL_10021_x("10021_x", "Y", "BL", "财务公司", "财务公司"),
	STL_10034_x("10034_x", "Y", "", "财务公司", "财务公司"),
	STL_10001_y("10001_y", "Y", "BL", "政企分公司", "财务部"),
	STL_10007_y("10007_y", "Y", "BL", "财务公司", "财务公司"),
	STL_10021_y("10021_y", "Y", "BL", "财务公司", "财务公司"),
	STL_10034_y("10034_y", "Y", "", "财务公司", "财务公司");
	
	private String rKey;

	private String adjust;

	private String bArbl;

	private String firstLv;

	private String secondLv;

	/** 
	 * @Title: isRaqKey 
	 * @Description: 返回对应枚举
	 * @param @param key
	 * @param @return    参数 
	 * @return ConstantsEnum    返回类型 
	 * @throws 
	 */ 
	public static ParametersEnum isR_KEY(String key){
		for(ParametersEnum pe : ParametersEnum.values()){
			if(key.equals(pe.getRKey())){
				return pe;
			}
		}
		return null;
	}
	
}
