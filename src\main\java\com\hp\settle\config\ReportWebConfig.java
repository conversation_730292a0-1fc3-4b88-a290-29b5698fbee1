package com.hp.settle.config;

import cn.hutool.core.util.StrUtil;
import com.raqsoft.report.view.ReportServlet;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.web.servlet.ServletRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;

/**
 * Project: boss-service-settle
 * Date : 2023/10/8 17:15
 * Author : hw
 * Description : 统计报表servlet注册
 */
@Configuration
@Slf4j
public class ReportWebConfig {
    @Autowired
    private Environment environment;

    @Bean
    public ServletRegistrationBean servletRegistrationBean() {
        ServletRegistrationBean registration = new ServletRegistrationBean(new ReportServlet());
        registration.setLoadOnStartup(1);
        String activeProfile = environment.getActiveProfiles()[0];
        String nacosServer = System.getenv("NACOS_SERVER");
        log.info("NACOS_SERVER:{}", nacosServer);
        if (activeProfile.contains("lt")) {
            log.info("联调环境部署-获取连接环境配置文件 raqsoftConfig-sit.xml");
            registration.addInitParameter("configFile", "config/raqsoftConfig-sit.xml");
        } else if (activeProfile.contains("test") && StrUtil.contains(nacosServer, "bboss-billing")) {
            log.info("测试环境部署-获取连接环境配置文件 raqsoftConfig-test.xml");
            registration.addInitParameter("configFile", "config/raqsoftConfig-test.xml");
        } else {
            log.info("开发环境部署-配置文件：raqsoftConfig.xml");
            registration.addInitParameter("configFile", "config/raqsoftConfig.xml");
        }
        //必须得支持reportServlet从类路径下读xml才可
        registration.addInitParameter("headless", "none");
        registration.setName("reportServlet");
        registration.addUrlMappings("/reportServlet");
        com.raqsoft.report.view.ServletMappings.mappings.put( "com.raqsoft.report.view.ReportServlet", "/reportServlet");
        System.out.println("润乾servlet注册完成");
        return registration;
    }
}
