package com.hp.settle.controller;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.hp.settle.api.R;
import com.hp.settle.service.ReportBatchService;
import com.hp.settle.util.CommonUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StopWatch;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.concurrent.locks.ReentrantLock;

@Slf4j
@RestController
@RequestMapping("/api")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ReportBatchController {

    private static ReentrantLock evictLock = new ReentrantLock();
    private static ReentrantLock downloadLock = new ReentrantLock();

    private final ReportBatchService reportBatchService;
    // 报表预生成 + 批量下载  （先后台预生成一个压缩包文件  然后页面再批量下载）
    // http://127.0.0.1:8081/reportBatch?settlemonth=202310&func=10015,10075
    // http://127.0.0.1:8081/reportBatch?settlemonth=202310&func=10001,10002,10015,10074,10075,10007,10008,10009,10010,10021,10022,10019,10020,10034,10090,10091,10036,10037,10069,10027
    // http://localhost:8081/reportJsp/ReportBatch.jsp?bssnum=000admin&elemCode=settlementV3
    @PostMapping("reportBatch")
    public R<String> reportBatch(HttpServletRequest request) {
        if (!downloadLock.tryLock()) {
            log.info("reportBatch报表预生成正在执行，请稍后再试");
            return R.error("报表预生成正在执行，请稍后再试");
        }
        String reqId = request.getSession().getId();
        MDC.put("traceId", reqId);
        log.info("reportBatch报表预生成start");
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        CommonUtil.shareRequest(request);
        try {
            request.setCharacterEncoding(String.valueOf(StandardCharsets.UTF_8));
            return reportBatchService.doFiles(request);

        } catch (Exception e) {
            log.error("reportBatch报表预生成error:{}", e.getMessage(), e);
            return R.error("报表批量导出未成功");
        }finally {
            stopWatch.stop();
            log.info("reportBatch报表预生成耗时：{}ms", stopWatch.getTotalTimeMillis());
            downloadLock.unlock();
            MDC.clear();
            CommonUtil.remove();
        }
    }

    @PostMapping("evictReportBatch")
    public R<String> evictReportBatch(@RequestBody Map<String,String> content) {
        if (!evictLock.tryLock()) {
            log.info("evictReportBatch 报表清理->生成正在执行，请稍后再试");
            return R.ok("evictReportBatch 报表清理->生成正在执行，请稍后再试",1);
        }
        String reqId = IdUtil.fastSimpleUUID();
        MDC.put("traceId", reqId);
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        try {
            log.info("evictReportBatch 报表清理->生成.start");
            log.info("请求参数:{}", JSONUtil.toJsonStr(content));
            String raqList = content.get("raqList");
            if (StringUtils.isBlank(raqList)) {
                throw new RuntimeException("raqList不能为空");
            }
            reportBatchService.evictReportBatch(content);
        } catch (Exception e) {
            log.error("evictReportBatch 报表清理->生成.error:{}", e.getMessage(), e);
            return R.error(e.getMessage());
        }finally {
            stopWatch.stop();
            log.info("evictReportBatch 报表清理->生成.end：{}ms", stopWatch.getTotalTimeMillis());
            evictLock.unlock();
            MDC.clear();
            CommonUtil.remove();
        }
        return R.ok("success",1);
    }

    @GetMapping("/reportBatchDownload")
    @Deprecated
    public void downloadBatch(HttpServletRequest request, HttpServletResponse response) {
        try {
            request.setCharacterEncoding(String.valueOf(StandardCharsets.UTF_8));
            response.setCharacterEncoding(String.valueOf(StandardCharsets.UTF_8));
            String id = request.getSession().getId();
            log.info("reportBatchDownload-sessionId:{}", id);
            MDC.put("traceId", id);
            reportBatchService.downloadBatch(request, response);
            log.info("reportBatchDownload批量下载成功");
        } catch (Exception e) {
            log.error("reportBatchDownload批量下载失败error:{}", e.getMessage(), e);
        }finally {
            MDC.clear();
        }
    }

    @PostMapping("/reportDownload")
    public void reportDownload(HttpServletRequest request, HttpServletResponse response, @RequestBody Map<String,Object> map) {
        try {
            request.setCharacterEncoding(String.valueOf(StandardCharsets.UTF_8));
            response.setCharacterEncoding(String.valueOf(StandardCharsets.UTF_8));
            String id = request.getSession().getId();
            log.info(JSONUtil.toJsonStr(map));
            log.info("reportDownload-sessionId:{}", id);
            MDC.put("traceId", id);
            reportBatchService.downloadBatch(request, response,map);
            log.info("reportBatchDownload批量下载成功");
        } catch (Exception e) {
            log.error("reportBatchDownload批量下载失败error:{}", e.getMessage(), e);
        }finally {
            MDC.clear();
        }
    }

}
