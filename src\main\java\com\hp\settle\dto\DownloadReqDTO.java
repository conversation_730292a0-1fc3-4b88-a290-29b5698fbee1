package com.hp.settle.dto;


import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import lombok.Data;

@Data
public class DownloadReqDTO {

    private String raqList;

    private String settlemonth;
    private String adjust;
    private String taxrate;
    private String spid;
    private String entity;
    private String bat;
    private String version;
    private String b_arbl;
    private String R_KEY;
    private String rpx;
    private String tree_node_id;
    private String prov;
    private String uid ;

    public String getAdjustName() {
        String adjStr = "";
        String _adjust = StringUtils.isBlank(adjust) ? "9" : adjust;
        if (_adjust.equals("9") || _adjust.equals("0")) {
            adjStr = "";
        } else if (adjust.equals("1")) {
            adjStr = "_调账_";
        } else {
            adjStr = "";
        }
        return adjStr;
    }

    public String getAdjustAllName() {
        String adjAllStr = "";
        String _adjust = StringUtils.isBlank(adjust) ? "0" : adjust;
        switch (_adjust) {
            case "0":
                adjAllStr = "(全部)";
                break;
            case "1":
                adjAllStr = "";
                break;
            case "2":
                adjAllStr = "_调账_";
                break;
            default:
                adjAllStr = "";
                break;
        }
        return adjAllStr;
    }

    public String getModAdjust() {
       return StrUtil.isBlank(adjust)? "0":adjust;
    }
}