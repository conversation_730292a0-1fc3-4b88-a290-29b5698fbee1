package com.hp.settle.config;

import com.alibaba.nacos.api.config.annotation.NacosConfigurationProperties;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;


@Data
@Configuration
@ConfigurationProperties(prefix = "goldbank.config")
@NacosConfigurationProperties(dataId = "settle-service-report", autoRefreshed = true)
public class GoldBankConfig {


    private String paramUrl;


    private String verifyTicketUrl;

    private String keyExpireTime;

    /**
     * 菜单转发路径; layout/..
     */
    private String currentNode;

    /**
     * 服务名称
     */
    private String svcName;

    /**
     * 接口地址
     */
    private String uri;

    /**
     * 金库操作编码; #|#分隔字段，操作编码/票据有效期/操作描述/应用操作的菜单编号，对应于权限系统ID/应用操作的菜单名称
     */
    private String goldBankCode;

}
