package com.hp.settle.controller;

import com.hp.settle.service.BillCycleService;
import com.hp.settle.vo.BaseRspsMsg;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;


@Controller
@Slf4j
@RequestMapping("/settle")
public class BIllCycleController {

    @Autowired
    private BillCycleService billCycleService;

    /**
     * 功能描述: 获取账期
     */
    @RequestMapping(value = "/getAllAcctMonth", method = RequestMethod.GET)
    @ResponseBody
    public BaseRspsMsg getAllAcctMonth()  {

        BaseRspsMsg baseRspsMsg ;
        try{
//            JwtPayload jwtPayload = (JwtPayload)request.getAttribute(JwtPayload.class.getName());
            //全中心越权访问问题，前端传的staffnum和token获取的要一致，不一致则认为权限有问题
            //staffNum =  RSAUtils.decryptBase64ByPrivateKey(staffNum);
//            log.info("客户信息:{}",staffNum);
//            log.info("-->queryJobSpecByJobspecIdJobTypeId,jwtPayload:{}",jwtPayload==null?null: JSON.toJSONString(jwtPayload));
//            if (jwtPayload==null || StringUtils.isBlank(jwtPayload.getName()) || StringUtils.isBlank(staffNum) || !jwtPayload.getName().equals(staffNum)) {
//                baseRspsMsg = BaseRspsMsg.build(BaseRspsMsg.BIZ_CODE_00001_FAILE, "鉴权失败。");
//                return baseRspsMsg;
//            }
            baseRspsMsg = BaseRspsMsg.ok(billCycleService.getAllAcctMonth());
        } catch (Exception e){
            log.error("系统异常！", e);
            baseRspsMsg = BaseRspsMsg.fail("系统异常");
        }
        return baseRspsMsg;
    }

}
