package com.hp.settle.controller;

import com.hp.settle.service.CommonService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.nio.charset.StandardCharsets;

@Slf4j
@RestController
@RequestMapping("/common")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class CommonController {

    private final CommonService commonService;

    @PostMapping("/isCheck")
    public void isCheck(HttpServletRequest request, HttpServletResponse response) {
        log.info("***isCheck进入审核步骤start***");
        try {
            request.setCharacterEncoding(String.valueOf(StandardCharsets.UTF_8));
            response.setCharacterEncoding(String.valueOf(StandardCharsets.UTF_8));
            commonService.isCheck(request, response);
            log.info("***isCheck进入审核步骤end***");
        } catch (Exception e) {
            log.error("isCheck进入审核步骤error:{}", e.getMessage(), e);
        }
    }

    @PostMapping("/export")
    public void export(HttpServletRequest request, HttpServletResponse response) {
        log.info("***export批量下载start***");
        try {
            request.setCharacterEncoding(String.valueOf(StandardCharsets.UTF_8));
            response.setCharacterEncoding(String.valueOf(StandardCharsets.UTF_8));
            commonService.export(request, response);
            log.info("***export批量下载end***");
        } catch (Exception e) {
            log.error("export批量下载error:{}", e.getMessage(), e);
        }
    }


    @PostMapping("/yunweiDownloadFiles")
    public void yunweiDownloadFiles(HttpServletRequest request, HttpServletResponse response) {
        log.info("***yunweiDownloadFiles批量下载start***");
        try {
            request.setCharacterEncoding(String.valueOf(StandardCharsets.UTF_8));
            response.setCharacterEncoding(String.valueOf(StandardCharsets.UTF_8));
            commonService.yunweiDownloadFiles(request, response);
            log.info("***yunweiDownloadFiles批量下载end***");
        } catch (Exception e) {
            log.error("yunweiDownloadFiles批量下载error:{}", e.getMessage(), e);
        }
    }

    @GetMapping("/isDownloadZip")
    public void isDownloadZip(HttpServletRequest request, HttpServletResponse response) {
        log.info("***isDownloadZip批量下载start***");
        try {
            request.setCharacterEncoding(String.valueOf(StandardCharsets.UTF_8));
            response.setCharacterEncoding(String.valueOf(StandardCharsets.UTF_8));
            commonService.isDownloadZip(request, response);
            log.info("***isDownloadZip批量下载end***");
        } catch (Exception e) {
            log.error("isDownloadZip批量下载error:{}", e.getMessage(), e);
        }
    }


}
