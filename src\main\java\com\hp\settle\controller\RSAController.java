package com.hp.settle.controller;

import com.hp.settle.service.ReportCheckService;
import com.hp.settle.vo.BaseRspsMsg;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * Project: boss-service-runqianreport
 * Date : 2023/11/14 18:55
 * Author : hw
 * Description : 1.获取公钥
 */
@RestController
@Slf4j
@RequestMapping("/rsa")
public class RSAController {

    @Autowired
    private ReportCheckService reportCheckService;

    @RequestMapping(value = "/getPublicKey", method = RequestMethod.GET)
    public BaseRspsMsg getPublicKey(String staffNum){
        BaseRspsMsg baseRspsMsg ;
        return BaseRspsMsg.ok(reportCheckService.insert<PERSON>eyPair(staffNum));
    }
}
