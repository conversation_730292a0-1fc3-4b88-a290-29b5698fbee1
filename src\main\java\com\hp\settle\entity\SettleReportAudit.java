package com.hp.settle.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.List;

/**
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class SettleReportAudit {

    private static final long serialVersionUID = 5591634336055716860L;


    //private String treeType;

   // private String acctMonth;

    private String staffNum;

    private List<ReportAudit> reportAudit;


}
