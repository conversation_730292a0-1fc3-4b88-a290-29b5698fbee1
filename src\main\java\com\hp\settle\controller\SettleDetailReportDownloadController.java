package com.hp.settle.controller;

import cn.hutool.core.util.IdUtil;
import com.hp.settle.api.R;
import com.hp.settle.dto.SettleDetailDownloadReqDTO;
import com.hp.settle.service.SettleDetailReportExcelService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StopWatch;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReentrantLock;

/**
 * 结算明细报表生产：5g消息和双跨专线
 */
@Slf4j
@RestController
@RequestMapping("/settleDetailReport")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class SettleDetailReportDownloadController {
    @Resource
    private SettleDetailReportExcelService specialLineReportExcelService;

    private Map<String, ReentrantLock> raqLockMap = new ConcurrentHashMap<>();


    @PostMapping(value = "/isDownloadExcel")
    public R isDownloadExcel(@RequestBody SettleDetailDownloadReqDTO reqDTO) {
        String reqId = IdUtil.fastSimpleUUID();
        MDC.put("traceId", reqId);
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        try {
            return specialLineReportExcelService.isDownloadExcel(reqDTO);
        }catch (Exception e) {
            log.error("报表导出异常", e);
            return R.error("生成文件失败,请稍后重试");
        }finally {
            stopWatch.stop();
            log.info("生成文件结束,耗时：{}", stopWatch.getTotalTimeMillis());
            MDC.clear();
        }
    }
}
