package com.hp.settle.controller;

import cn.hutool.core.util.IdUtil;
import com.hp.settle.api.R;
import com.hp.settle.constants.CommonConstants;
import com.hp.settle.dto.DownloadReqDTO;
import com.hp.settle.dto.DownloadRspDTO;
import com.hp.settle.service.ZipExcelService;
import com.hp.settle.util.CommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.util.StopWatch;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReentrantLock;

/**
 * Project: boss-service-settle
 * Date : 2023/10/24 10:08
 * Author : hw
 * Description :打包PDF Excel并压缩为Zip
 */
@RestController
@Slf4j
@RequestMapping("/download")
public class ZipController {

    @Resource
    private ZipExcelService zipExcelService;

    private Map<String, ReentrantLock> raqLockMap = new ConcurrentHashMap<>();

    @RequestMapping(value = "/isDownloadExcelZip", method = RequestMethod.POST)
    public R<DownloadRspDTO> isDownloadExcelZip(HttpServletRequest request, DownloadReqDTO reqDTO) {
        String key = reqDTO.getSettlemonth() + "_" + reqDTO.getR_KEY()+"_"+ CommonConstants.FOLDER_EXCEL;
        ReentrantLock lock = raqLockMap.computeIfAbsent(key, k -> new ReentrantLock());
        if (!lock.tryLock()) {
            log.info("报表：{},R_KEY:{}，EXCEL批量导出正在执行，请稍后再试",reqDTO.getRpx(),reqDTO.getR_KEY());
            return R.error("EXCEL批量导出正在执行，请稍后再试");
        }
        String reqId = IdUtil.fastSimpleUUID();
        MDC.put("traceId", reqId);
        reqDTO.setUid(reqId);
        log.info("报表：{},R_KEY:{}，EXCEL批量导出开始",reqDTO.getRpx(),reqDTO.getR_KEY());
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        CommonUtil.shareRequest(request);
        try {
            return zipExcelService.isDownloadExcelZip(reqDTO);
        }catch (Exception e) {
            log.error("报表：{},R_KEY:{}，EXCEL批量导出异常", reqDTO.getRpx(), reqDTO.getR_KEY(), e);
            return R.error("生成Excel压缩报表失败,请稍后重试");
        }finally {
            stopWatch.stop();
            log.info("报表：{},R_KEY:{}，EXCEL批量导出结束,耗时：{}", reqDTO.getRpx(), reqDTO.getR_KEY(), stopWatch.getTotalTimeMillis());
            MDC.clear();
            lock.unlock();
            CommonUtil.remove();
        }

    }

    @RequestMapping(value = "/isDownloadPdfZip", method = RequestMethod.POST)
    public R<DownloadRspDTO> isDownloadPdfZip(HttpServletRequest request, DownloadReqDTO reqDTO) {
        String key = reqDTO.getSettlemonth() + "_" + reqDTO.getR_KEY()+"_"+ CommonConstants.FOLDER_PDF;
        ReentrantLock lock = raqLockMap.computeIfAbsent(key, k -> new ReentrantLock());
        if (!lock.tryLock()) {
            log.info("报表：{},R_KEY:{}，PDF批量导出正在执行，请稍后再试",reqDTO.getRpx(),reqDTO.getR_KEY());
            return R.error("PDF批量导出正在执行，请稍后再试");
        }
        String reqId = IdUtil.fastSimpleUUID();
        MDC.put("traceId", reqId);
        reqDTO.setUid(reqId);
        log.info("报表：{},R_KEY:{}，PDF批量导出开始",reqDTO.getRpx(),reqDTO.getR_KEY());
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        CommonUtil.shareRequest(request);
        try {
            return zipExcelService.isDownloadPdfZip(reqDTO);
        }catch (Exception e) {
            log.error("报表：{},R_KEY:{}，PDF批量导出异常", reqDTO.getRpx(), reqDTO.getR_KEY(), e);
            return R.error("生成PDF压缩报表失败,请稍后重试");
        }finally {
            stopWatch.stop();
            log.info("报表：{},R_KEY:{}，PDF批量导出结束,耗时：{}", reqDTO.getRpx(), reqDTO.getR_KEY(), stopWatch.getTotalTimeMillis());
            MDC.clear();
            lock.unlock();
            CommonUtil.remove();
        }
    }

    @RequestMapping(value = "/exportDownloadZip", method = RequestMethod.GET)
    public void exportDownloadZip(HttpServletRequest request,
                                  HttpServletResponse response) {
        zipExcelService.exportDownloadZip(request, response);
    }


}
