package com.hp.settle.controller;

import cn.hutool.core.date.StopWatch;
import com.hp.settle.api.R;
import com.hp.settle.service.ExportRaqService;
import com.hp.settle.util.CommonUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.locks.ReentrantLock;

/**
 * @Classname ExportRaqController
 * @Description 暂估/销暂估单批量下载
 * @Date 2023/11/14 17:20
 * <AUTHOR>
 * @Version 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/exportRaq")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ExportRaqController {

    private final ExportRaqService exportRaqService;
    private final static ReentrantLock lock = new ReentrantLock();
    // 暂估/销暂估单批量下载
    // http://localhost:9232/exportRaq?settlemonth=202310&raqType=1
    @GetMapping
    public R getExports(HttpServletRequest request,
                        @RequestParam(value = "settlemonth") String acctMonth,
                        @RequestParam(value = "raqType") String raqType,
                        @RequestParam(value = "raqKey",required = false) String raqKey) {
        log.info("***暂估/销暂估单批量下载start***");
        log.info("settlemonth:{},raqType:{},raqKey:{}", acctMonth, raqType, raqKey);
        if (!lock.tryLock()) {
            log.info("暂估/销暂估单批量下载正在执行，请稍后再试");
            return R.error("暂估/销暂估单批量下载正在执行，请稍后再试");
        }
        StopWatch stopWatch = new StopWatch();
        try {
            stopWatch.start();
            CommonUtil.shareRequest(request);
            request.setCharacterEncoding(String.valueOf(StandardCharsets.UTF_8));
            String filePath = exportRaqService.doFiles( acctMonth, raqType,raqKey);
            return R.ok(filePath,1);
        } catch (Exception e) {
            log.error("暂估/销暂估单批量下载error:{}", e.getMessage(), e);
            return R.error(e.getMessage());
        }finally {
            stopWatch.stop();
            log.info("***暂估/销暂估单批量下载end***");
            log.info("暂估/销暂估单批量下载总耗时：{} ms", stopWatch.getTotalTimeMillis());
            lock.unlock();
            CommonUtil.remove();
        }
    }

}
