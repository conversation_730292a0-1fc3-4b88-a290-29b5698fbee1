//package com.hp.settle.config;
//
//import lombok.Data;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.context.properties.ConfigurationProperties;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.security.config.annotation.web.builders.HttpSecurity;
//import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
//import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
//import org.springframework.stereotype.Component;
//
//import java.util.List;
//
//@Configuration
//@EnableWebSecurity
//public class WebSecurityConfig extends WebSecurityConfigurerAdapter {
//
//    @Autowired
//    private SecurityBean securityBean;
//    @Override
//    protected void configure(HttpSecurity http) throws Exception {
//        http
//            .authorizeRequests()
//            .antMatchers(securityBean.whiteList.toArray(new String[0])).permitAll()
//            .antMatchers(securityBean.blackList.toArray(new String[0])).authenticated()
//            .and()
//            .httpBasic()
//            .and()
//            .authorizeRequests().anyRequest().permitAll();
//
//        http.csrf().disable();
//    }
//
//    @Data
//    @Component
//    @ConfigurationProperties(prefix = "security") // 配置文件的前缀
//    public static class SecurityBean{
//        public List<String> whiteList;
//        public List<String> blackList;
//    }
//}