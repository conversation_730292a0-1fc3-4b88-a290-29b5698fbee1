package com.hp.settle.dao;

import org.springframework.stereotype.Repository;

/**
 * Project: boss-service-settle
 * Date : 2023/10/16 12:10
 * Author : hw
 * Description :
 */
@Repository
public class ParameterDao {

//    @Autowired
//    private JdbcTemplate jdbcTemplate;
//
//    public String getModifiedParameter() {
//        // 执行数据库查询操作，获取需要修改的参数
//        String modifiedParameter = jdbcTemplate.queryForObject("SELECT modified_parameter FROM parameter_table", String.class);
//        return modifiedParameter;
//    }
}
