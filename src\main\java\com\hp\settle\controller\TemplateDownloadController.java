package com.hp.settle.controller;

import com.hp.settle.service.TemplateDownloadService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.nio.charset.StandardCharsets;

@Slf4j
@RestController
@RequestMapping("/templateDownload")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class TemplateDownloadController {

    private final TemplateDownloadService templateDownloadService;
    // 结算V3模板下载
    // http://localhost:9232/settleReport/templateDownload?R_KEY=10035&bssnum=yunwei&elemCode=settlementV7&settlemonth=202310
    @GetMapping
    public void get(HttpServletRequest request, HttpServletResponse response) {
        log.info("***templateDownload结算V3模板下载start***");
        try {
            request.setCharacterEncoding(String.valueOf(StandardCharsets.UTF_8));
            response.setCharacterEncoding(String.valueOf(StandardCharsets.UTF_8));
            templateDownloadService.doFiles(request, response);
            log.info("***templateDownload结算V3模板下载end***");
        } catch (Exception e) {
            log.error("templateDownload结算V3模板下载error:{}", e.getMessage(), e);
        }
    }

    @PostMapping
    public void post(HttpServletRequest request, HttpServletResponse response) {
        log.info("***templateDownload结算V3模板下载start***");
        try {
            request.setCharacterEncoding(String.valueOf(StandardCharsets.UTF_8));
            response.setCharacterEncoding(String.valueOf(StandardCharsets.UTF_8));
            templateDownloadService.doFiles(request, response);
            log.info("***templateDownload结算V3模板下载end***");
        } catch (Exception e) {
            log.error("templateDownload结算V3模板下载error:{}", e.getMessage(), e);
        }
    }

}
