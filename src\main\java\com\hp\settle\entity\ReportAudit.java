package com.hp.settle.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

@Data
@Slf4j
@JsonIgnoreProperties(ignoreUnknown = true)
public class ReportAudit {

    @JsonProperty(value = "id")
    private String id;

    @JsonProperty(value = "pId")
    private String pId;

    @JsonProperty(value = "name")
    private String name;

    @JsonProperty(value = "nodeUrl")
    private String nodeUrl;

    @JsonProperty(value = "treeNodeId")
    private int treeNodeId;

    @JsonProperty(value = "nodeLevel")
    private int nodeLevel;

    @JsonProperty(value = "isLeaf")
    private String isLeaf;

    @JsonProperty(value = "checked")
    private Boolean checked;

    @JsonProperty(value = "staffNum")
    private String staffNum;

    @JsonProperty(value = "acctMonth")
    private int acctMonth;


    @Override
    public String toString() {
        return "SettleReportResp{" +
                "id='" + id + '\'' +
                ", pId='" + pId + '\'' +
                ", name='" + name + '\'' +
                ", nodeUrl='" + nodeUrl + '\'' +
                ", treeNodeId=" + treeNodeId +
                ", nodeLevel=" + nodeLevel +
                ", isLeaf='" + isLeaf + '\'' +
                ", checked='" + checked + '\'' +
                '}';
    }
}
