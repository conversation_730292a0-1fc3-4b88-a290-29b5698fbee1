[2025-07-17 12:37:15][INFO ][main][o.s.b.SpringApplication.logStartupProfileInfo:637][]:The following 1 profile is active: "dev"
[2025-07-17 12:37:16][INFO ][main][o.s.d.r.c.RepositoryConfigurationDelegate.multipleStoresDetected:262][]:Multiple Spring Data modules found, entering strict repository configuration mode
[2025-07-17 12:37:16][INFO ][main][o.s.d.r.c.RepositoryConfigurationDelegate.registerRepositoriesIn:132][]:Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[2025-07-17 12:37:16][INFO ][main][o.s.d.r.c.RepositoryConfigurationDelegate.registerRepositoriesIn:201][]:Finished Spring Data repository scanning in 19 ms. Found 0 Redis repository interfaces.
[2025-07-17 12:37:16][INFO ][main][o.s.c.c.s.GenericScope.setSerializationId:283][]:BeanFactory id=dbe784e5-818f-3f46-bd86-89a8e46f5497
[2025-07-17 12:37:16][INFO ][main][c.u.j.c.EnableEncryptablePropertiesBeanFactoryPostProcessor.postProcessBeanFactory:39][]:Post-processing PropertySource instances
[2025-07-17 12:37:16][INFO ][main][c.u.j.EncryptablePropertySourceConverter.makeEncryptable:56][]:Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[2025-07-17 12:37:16][INFO ][main][c.u.j.EncryptablePropertySourceConverter.makeEncryptable:56][]:Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[2025-07-17 12:37:16][INFO ][main][c.u.j.EncryptablePropertySourceConverter.makeEncryptable:56][]:Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[2025-07-17 12:37:16][INFO ][main][c.u.j.EncryptablePropertySourceConverter.makeEncryptable:56][]:Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[2025-07-17 12:37:16][INFO ][main][c.u.j.EncryptablePropertySourceConverter.makeEncryptable:56][]:Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[2025-07-17 12:37:16][INFO ][main][c.u.j.EncryptablePropertySourceConverter.makeEncryptable:56][]:Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[2025-07-17 12:37:16][INFO ][main][c.u.j.EncryptablePropertySourceConverter.makeEncryptable:56][]:Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[2025-07-17 12:37:16][INFO ][main][c.u.j.EncryptablePropertySourceConverter.makeEncryptable:56][]:Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[2025-07-17 12:37:16][INFO ][main][c.u.j.EncryptablePropertySourceConverter.makeEncryptable:56][]:Converting PropertySource Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[2025-07-17 12:37:16][INFO ][main][c.u.j.EncryptablePropertySourceConverter.makeEncryptable:56][]:Converting PropertySource Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[2025-07-17 12:37:16][INFO ][main][c.u.j.EncryptablePropertySourceConverter.makeEncryptable:56][]:Converting PropertySource Config resource 'class path resource [application.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[2025-07-17 12:37:16][INFO ][main][c.u.j.EncryptablePropertySourceConverter.makeEncryptable:56][]:Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[2025-07-17 12:37:16][INFO ][main][c.u.j.EncryptablePropertySourceConverter.makeEncryptable:56][]:Converting PropertySource defaultProperties [org.springframework.boot.DefaultPropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[2025-07-17 12:37:16][INFO ][main][c.u.j.f.DefaultLazyPropertyFilter.lambda$null$2:31][]:Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[2025-07-17 12:37:16][INFO ][main][c.u.j.r.DefaultLazyPropertyResolver.lambda$null$2:35][]:Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[2025-07-17 12:37:16][INFO ][main][c.u.j.d.DefaultLazyPropertyDetector.lambda$null$2:35][]:Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[2025-07-17 12:37:16][DEBUG][main][o.s.b.w.s.s.DocumentRoot.getArchiveFileDocumentRoot:81][]:Code archive: D:\04server\apache-maven-3.6.2\repository3\org\springframework\boot\spring-boot\2.7.6\spring-boot-2.7.6.jar
[2025-07-17 12:37:16][DEBUG][main][o.s.b.w.s.s.DocumentRoot.getExplodedWarFileDocumentRoot:125][]:Code archive: D:\04server\apache-maven-3.6.2\repository3\org\springframework\boot\spring-boot\2.7.6\spring-boot-2.7.6.jar
[2025-07-17 12:37:16][DEBUG][main][o.s.b.w.s.s.DocumentRoot.getValidDirectory:69][]:Document root: D:\05code\newsettle\settle-service-report\src\main\webapp
[2025-07-17 12:37:17][INFO ][main][o.s.b.w.e.t.TomcatWebServer.initialize:108][]:Tomcat initialized with port(s): 9232 (http)
[2025-07-17 12:37:17][INFO ][main][o.a.j.l.DirectJDKLog.log:173][]:Initializing ProtocolHandler ["http-nio-9232"]
[2025-07-17 12:37:17][INFO ][main][o.a.j.l.DirectJDKLog.log:173][]:Starting service [Tomcat]
[2025-07-17 12:37:17][INFO ][main][o.a.j.l.DirectJDKLog.log:173][]:Starting Servlet engine: [Apache Tomcat/9.0.69]
[2025-07-17 12:37:17][INFO ][main][o.a.j.l.DirectJDKLog.log:173][]:At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
[2025-07-17 12:37:17][INFO ][main][o.a.j.l.DirectJDKLog.log:173][]:Initializing Spring embedded WebApplicationContext
[2025-07-17 12:37:17][INFO ][main][o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:292][]:Root WebApplicationContext: initialization completed in 2254 ms
[2025-07-17 12:37:17][INFO ][main][c.h.s.c.ReportWebConfig.servletRegistrationBean:30][]:NACOS_SERVER:null
[2025-07-17 12:37:17][INFO ][main][c.h.s.c.ReportWebConfig.servletRegistrationBean:38][]:开发环境部署-配置文件：raqsoftConfig.xml
[2025-07-17 12:37:17][INFO ][main][c.a.d.s.b.a.DruidDataSourceAutoConfigure.dataSource:56][]:Init DruidDataSource
[2025-07-17 12:37:18][INFO ][main][c.a.d.p.DruidDataSource.init:985][]:{dataSource-1} inited
[2025-07-17 12:37:19][INFO ][main][c.h.c.b.s.c.r.RedisPoolConfiguration.redisPoolConfig:279][]:redis 连接池配置
[2025-07-17 12:37:19][INFO ][main][c.h.c.b.s.c.r.RedisPoolConfiguration.redisConnectionFactory:94][]:redis factory 加载
[2025-07-17 12:37:19][INFO ][main][c.h.c.b.s.c.r.RedisPoolConfiguration.getRedisStandaloneConfiguration:230][]:redis 单机配置
[2025-07-17 12:37:20][WARN ][main][c.b.m.c.m.TableInfoHelper.initTableFields:342][]:Can not find table primary key in Class: "com.hp.settle.entity.ReportGoldConfig".
[2025-07-17 12:37:20][WARN ][main][c.b.m.c.i.DefaultSqlInjector.getMethodList:56][]:class com.hp.settle.entity.ReportGoldConfig ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[2025-07-17 12:37:20][INFO ][main][c.h.c.b.s.c.r.RedisConfig.redisTemplate:29][]:使用lettuce连接
[2025-07-17 12:37:21][INFO ][main][s.d.s.w.WebMvcPropertySourcedRequestMappingHandlerMapping.initHandlerMethods:69][]:Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
[2025-07-17 12:37:23][INFO ][main][o.s.c.c.u.InetUtils.convertAddress:170][]:Cannot determine local hostname
[2025-07-17 12:37:23][INFO ][main][o.a.j.l.DirectJDKLog.log:173][]:Starting ProtocolHandler ["http-nio-9232"]
[2025-07-17 12:37:25][INFO ][main][o.s.b.w.e.t.TomcatWebServer.start:220][]:Tomcat started on port(s): 9232 (http) with context path '/settleReport'
[2025-07-17 12:37:26][INFO ][main][o.s.c.c.u.InetUtils.convertAddress:170][]:Cannot determine local hostname
[2025-07-17 12:37:26][INFO ][main][s.d.s.w.p.DocumentationPluginsBootstrapper.start:93][]:Documentation plugins bootstrapped
[2025-07-17 12:37:26][INFO ][main][s.d.s.w.p.AbstractDocumentationPluginsBootstrapper.bootstrapDocumentationPlugins:79][]:Found 1 custom documentation plugin(s)
[2025-07-17 12:37:26][INFO ][main][s.d.s.w.s.ApiListingReferenceScanner.scan:44][]:Scanning for api listing references
[2025-07-17 12:37:27][INFO ][main][o.s.b.StartupInfoLogger.logStarted:61][]:Started BossServiceSettleApplication in 16.368 seconds (JVM running for 18.252)
[2025-07-17 13:17:52][INFO ][main][o.s.b.SpringApplication.logStartupProfileInfo:637][]:The following 1 profile is active: "dev"
[2025-07-17 13:17:53][INFO ][main][o.s.d.r.c.RepositoryConfigurationDelegate.multipleStoresDetected:262][]:Multiple Spring Data modules found, entering strict repository configuration mode
[2025-07-17 13:17:53][INFO ][main][o.s.d.r.c.RepositoryConfigurationDelegate.registerRepositoriesIn:132][]:Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[2025-07-17 13:17:53][INFO ][main][o.s.d.r.c.RepositoryConfigurationDelegate.registerRepositoriesIn:201][]:Finished Spring Data repository scanning in 17 ms. Found 0 Redis repository interfaces.
[2025-07-17 13:17:53][INFO ][main][o.s.c.c.s.GenericScope.setSerializationId:283][]:BeanFactory id=dbe784e5-818f-3f46-bd86-89a8e46f5497
[2025-07-17 13:17:53][INFO ][main][c.u.j.c.EnableEncryptablePropertiesBeanFactoryPostProcessor.postProcessBeanFactory:39][]:Post-processing PropertySource instances
[2025-07-17 13:17:53][INFO ][main][c.u.j.EncryptablePropertySourceConverter.makeEncryptable:56][]:Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[2025-07-17 13:17:53][INFO ][main][c.u.j.EncryptablePropertySourceConverter.makeEncryptable:56][]:Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[2025-07-17 13:17:53][INFO ][main][c.u.j.EncryptablePropertySourceConverter.makeEncryptable:56][]:Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[2025-07-17 13:17:53][INFO ][main][c.u.j.EncryptablePropertySourceConverter.makeEncryptable:56][]:Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[2025-07-17 13:17:53][INFO ][main][c.u.j.EncryptablePropertySourceConverter.makeEncryptable:56][]:Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[2025-07-17 13:17:53][INFO ][main][c.u.j.EncryptablePropertySourceConverter.makeEncryptable:56][]:Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[2025-07-17 13:17:53][INFO ][main][c.u.j.EncryptablePropertySourceConverter.makeEncryptable:56][]:Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[2025-07-17 13:17:53][INFO ][main][c.u.j.EncryptablePropertySourceConverter.makeEncryptable:56][]:Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[2025-07-17 13:17:53][INFO ][main][c.u.j.EncryptablePropertySourceConverter.makeEncryptable:56][]:Converting PropertySource Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[2025-07-17 13:17:53][INFO ][main][c.u.j.EncryptablePropertySourceConverter.makeEncryptable:56][]:Converting PropertySource Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[2025-07-17 13:17:53][INFO ][main][c.u.j.EncryptablePropertySourceConverter.makeEncryptable:56][]:Converting PropertySource Config resource 'class path resource [application.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[2025-07-17 13:17:53][INFO ][main][c.u.j.EncryptablePropertySourceConverter.makeEncryptable:56][]:Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[2025-07-17 13:17:53][INFO ][main][c.u.j.EncryptablePropertySourceConverter.makeEncryptable:56][]:Converting PropertySource defaultProperties [org.springframework.boot.DefaultPropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[2025-07-17 13:17:53][INFO ][main][c.u.j.f.DefaultLazyPropertyFilter.lambda$null$2:31][]:Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[2025-07-17 13:17:53][INFO ][main][c.u.j.r.DefaultLazyPropertyResolver.lambda$null$2:35][]:Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[2025-07-17 13:17:53][INFO ][main][c.u.j.d.DefaultLazyPropertyDetector.lambda$null$2:35][]:Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[2025-07-17 13:17:53][DEBUG][main][o.s.b.w.s.s.DocumentRoot.getArchiveFileDocumentRoot:81][]:Code archive: D:\04server\apache-maven-3.6.2\repository3\org\springframework\boot\spring-boot\2.7.6\spring-boot-2.7.6.jar
[2025-07-17 13:17:53][DEBUG][main][o.s.b.w.s.s.DocumentRoot.getExplodedWarFileDocumentRoot:125][]:Code archive: D:\04server\apache-maven-3.6.2\repository3\org\springframework\boot\spring-boot\2.7.6\spring-boot-2.7.6.jar
[2025-07-17 13:17:53][DEBUG][main][o.s.b.w.s.s.DocumentRoot.getValidDirectory:69][]:Document root: D:\05code\newsettle\settle-service-report\src\main\webapp
[2025-07-17 13:17:54][INFO ][main][o.s.b.w.e.t.TomcatWebServer.initialize:108][]:Tomcat initialized with port(s): 9232 (http)
[2025-07-17 13:17:54][INFO ][main][o.a.j.l.DirectJDKLog.log:173][]:Initializing ProtocolHandler ["http-nio-9232"]
[2025-07-17 13:17:54][INFO ][main][o.a.j.l.DirectJDKLog.log:173][]:Starting service [Tomcat]
[2025-07-17 13:17:54][INFO ][main][o.a.j.l.DirectJDKLog.log:173][]:Starting Servlet engine: [Apache Tomcat/9.0.69]
[2025-07-17 13:17:54][INFO ][main][o.a.j.l.DirectJDKLog.log:173][]:At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
[2025-07-17 13:17:54][INFO ][main][o.a.j.l.DirectJDKLog.log:173][]:Initializing Spring embedded WebApplicationContext
[2025-07-17 13:17:54][INFO ][main][o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:292][]:Root WebApplicationContext: initialization completed in 2215 ms
[2025-07-17 13:17:54][INFO ][main][c.h.s.c.ReportWebConfig.servletRegistrationBean:30][]:NACOS_SERVER:null
[2025-07-17 13:17:54][INFO ][main][c.h.s.c.ReportWebConfig.servletRegistrationBean:38][]:开发环境部署-配置文件：raqsoftConfig.xml
[2025-07-17 13:17:54][INFO ][main][c.a.d.s.b.a.DruidDataSourceAutoConfigure.dataSource:56][]:Init DruidDataSource
[2025-07-17 13:17:54][INFO ][main][c.a.d.p.DruidDataSource.init:985][]:{dataSource-1} inited
[2025-07-17 13:17:56][INFO ][main][c.h.c.b.s.c.r.RedisPoolConfiguration.redisPoolConfig:279][]:redis 连接池配置
[2025-07-17 13:17:56][INFO ][main][c.h.c.b.s.c.r.RedisPoolConfiguration.redisConnectionFactory:94][]:redis factory 加载
[2025-07-17 13:17:56][INFO ][main][c.h.c.b.s.c.r.RedisPoolConfiguration.getRedisStandaloneConfiguration:230][]:redis 单机配置
[2025-07-17 13:17:57][WARN ][main][c.b.m.c.m.TableInfoHelper.initTableFields:342][]:Can not find table primary key in Class: "com.hp.settle.entity.ReportGoldConfig".
[2025-07-17 13:17:57][WARN ][main][c.b.m.c.i.DefaultSqlInjector.getMethodList:56][]:class com.hp.settle.entity.ReportGoldConfig ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[2025-07-17 13:17:57][INFO ][main][c.h.c.b.s.c.r.RedisConfig.redisTemplate:29][]:使用lettuce连接
[2025-07-17 13:17:58][INFO ][main][s.d.s.w.WebMvcPropertySourcedRequestMappingHandlerMapping.initHandlerMethods:69][]:Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
[2025-07-17 13:18:00][INFO ][main][o.s.c.c.u.InetUtils.convertAddress:170][]:Cannot determine local hostname
[2025-07-17 13:18:00][INFO ][main][o.a.j.l.DirectJDKLog.log:173][]:Starting ProtocolHandler ["http-nio-9232"]
[2025-07-17 13:18:02][INFO ][main][o.s.b.w.e.t.TomcatWebServer.start:220][]:Tomcat started on port(s): 9232 (http) with context path '/settleReport'
[2025-07-17 13:18:03][INFO ][main][o.s.c.c.u.InetUtils.convertAddress:170][]:Cannot determine local hostname
[2025-07-17 13:18:03][INFO ][main][s.d.s.w.p.DocumentationPluginsBootstrapper.start:93][]:Documentation plugins bootstrapped
[2025-07-17 13:18:03][INFO ][main][s.d.s.w.p.AbstractDocumentationPluginsBootstrapper.bootstrapDocumentationPlugins:79][]:Found 1 custom documentation plugin(s)
[2025-07-17 13:18:04][INFO ][main][s.d.s.w.s.ApiListingReferenceScanner.scan:44][]:Scanning for api listing references
[2025-07-17 13:18:04][INFO ][main][o.s.b.StartupInfoLogger.logStarted:61][]:Started BossServiceSettleApplication in 16.79 seconds (JVM running for 18.221)
[2025-07-17 13:18:35][INFO ][main][o.s.b.SpringApplication.logStartupProfileInfo:637][]:The following 1 profile is active: "dev"
[2025-07-17 13:18:35][INFO ][main][o.s.d.r.c.RepositoryConfigurationDelegate.multipleStoresDetected:262][]:Multiple Spring Data modules found, entering strict repository configuration mode
[2025-07-17 13:18:35][INFO ][main][o.s.d.r.c.RepositoryConfigurationDelegate.registerRepositoriesIn:132][]:Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[2025-07-17 13:18:35][INFO ][main][o.s.d.r.c.RepositoryConfigurationDelegate.registerRepositoriesIn:201][]:Finished Spring Data repository scanning in 15 ms. Found 0 Redis repository interfaces.
[2025-07-17 13:18:36][INFO ][main][o.s.c.c.s.GenericScope.setSerializationId:283][]:BeanFactory id=dbe784e5-818f-3f46-bd86-89a8e46f5497
[2025-07-17 13:18:36][INFO ][main][c.u.j.c.EnableEncryptablePropertiesBeanFactoryPostProcessor.postProcessBeanFactory:39][]:Post-processing PropertySource instances
[2025-07-17 13:18:36][INFO ][main][c.u.j.EncryptablePropertySourceConverter.makeEncryptable:56][]:Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[2025-07-17 13:18:36][INFO ][main][c.u.j.EncryptablePropertySourceConverter.makeEncryptable:56][]:Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[2025-07-17 13:18:36][INFO ][main][c.u.j.EncryptablePropertySourceConverter.makeEncryptable:56][]:Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[2025-07-17 13:18:36][INFO ][main][c.u.j.EncryptablePropertySourceConverter.makeEncryptable:56][]:Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[2025-07-17 13:18:36][INFO ][main][c.u.j.EncryptablePropertySourceConverter.makeEncryptable:56][]:Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[2025-07-17 13:18:36][INFO ][main][c.u.j.EncryptablePropertySourceConverter.makeEncryptable:56][]:Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[2025-07-17 13:18:36][INFO ][main][c.u.j.EncryptablePropertySourceConverter.makeEncryptable:56][]:Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[2025-07-17 13:18:36][INFO ][main][c.u.j.EncryptablePropertySourceConverter.makeEncryptable:56][]:Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[2025-07-17 13:18:36][INFO ][main][c.u.j.EncryptablePropertySourceConverter.makeEncryptable:56][]:Converting PropertySource Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[2025-07-17 13:18:36][INFO ][main][c.u.j.EncryptablePropertySourceConverter.makeEncryptable:56][]:Converting PropertySource Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[2025-07-17 13:18:36][INFO ][main][c.u.j.EncryptablePropertySourceConverter.makeEncryptable:56][]:Converting PropertySource Config resource 'class path resource [application.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[2025-07-17 13:18:36][INFO ][main][c.u.j.EncryptablePropertySourceConverter.makeEncryptable:56][]:Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[2025-07-17 13:18:36][INFO ][main][c.u.j.EncryptablePropertySourceConverter.makeEncryptable:56][]:Converting PropertySource defaultProperties [org.springframework.boot.DefaultPropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[2025-07-17 13:18:36][INFO ][main][c.u.j.f.DefaultLazyPropertyFilter.lambda$null$2:31][]:Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[2025-07-17 13:18:36][INFO ][main][c.u.j.r.DefaultLazyPropertyResolver.lambda$null$2:35][]:Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[2025-07-17 13:18:36][INFO ][main][c.u.j.d.DefaultLazyPropertyDetector.lambda$null$2:35][]:Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[2025-07-17 13:18:36][DEBUG][main][o.s.b.w.s.s.DocumentRoot.getArchiveFileDocumentRoot:81][]:Code archive: D:\04server\apache-maven-3.6.2\repository3\org\springframework\boot\spring-boot\2.7.6\spring-boot-2.7.6.jar
[2025-07-17 13:18:36][DEBUG][main][o.s.b.w.s.s.DocumentRoot.getExplodedWarFileDocumentRoot:125][]:Code archive: D:\04server\apache-maven-3.6.2\repository3\org\springframework\boot\spring-boot\2.7.6\spring-boot-2.7.6.jar
[2025-07-17 13:18:36][DEBUG][main][o.s.b.w.s.s.DocumentRoot.getValidDirectory:69][]:Document root: D:\05code\newsettle\settle-service-report\src\main\webapp
[2025-07-17 13:18:36][INFO ][main][o.s.b.w.e.t.TomcatWebServer.initialize:108][]:Tomcat initialized with port(s): 9232 (http)
[2025-07-17 13:18:36][INFO ][main][o.a.j.l.DirectJDKLog.log:173][]:Initializing ProtocolHandler ["http-nio-9232"]
[2025-07-17 13:18:36][INFO ][main][o.a.j.l.DirectJDKLog.log:173][]:Starting service [Tomcat]
[2025-07-17 13:18:36][INFO ][main][o.a.j.l.DirectJDKLog.log:173][]:Starting Servlet engine: [Apache Tomcat/9.0.69]
[2025-07-17 13:18:37][INFO ][main][o.a.j.l.DirectJDKLog.log:173][]:At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
[2025-07-17 13:18:37][INFO ][main][o.a.j.l.DirectJDKLog.log:173][]:Initializing Spring embedded WebApplicationContext
[2025-07-17 13:18:37][INFO ][main][o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:292][]:Root WebApplicationContext: initialization completed in 1955 ms
[2025-07-17 13:18:37][INFO ][main][c.h.s.c.ReportWebConfig.servletRegistrationBean:30][]:NACOS_SERVER:null
[2025-07-17 13:18:37][INFO ][main][c.h.s.c.ReportWebConfig.servletRegistrationBean:38][]:开发环境部署-配置文件：raqsoftConfig.xml
[2025-07-17 13:18:37][INFO ][main][c.a.d.s.b.a.DruidDataSourceAutoConfigure.dataSource:56][]:Init DruidDataSource
[2025-07-17 13:18:37][INFO ][main][c.a.d.p.DruidDataSource.init:985][]:{dataSource-1} inited
[2025-07-17 13:18:38][INFO ][main][c.h.c.b.s.c.r.RedisPoolConfiguration.redisPoolConfig:279][]:redis 连接池配置
[2025-07-17 13:18:38][INFO ][main][c.h.c.b.s.c.r.RedisPoolConfiguration.redisConnectionFactory:94][]:redis factory 加载
[2025-07-17 13:18:38][INFO ][main][c.h.c.b.s.c.r.RedisPoolConfiguration.getRedisStandaloneConfiguration:230][]:redis 单机配置
[2025-07-17 13:18:39][WARN ][main][c.b.m.c.m.TableInfoHelper.initTableFields:342][]:Can not find table primary key in Class: "com.hp.settle.entity.ReportGoldConfig".
[2025-07-17 13:18:39][WARN ][main][c.b.m.c.i.DefaultSqlInjector.getMethodList:56][]:class com.hp.settle.entity.ReportGoldConfig ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[2025-07-17 13:18:39][INFO ][main][c.h.c.b.s.c.r.RedisConfig.redisTemplate:29][]:使用lettuce连接
[2025-07-17 13:18:40][INFO ][main][s.d.s.w.WebMvcPropertySourcedRequestMappingHandlerMapping.initHandlerMethods:69][]:Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
[2025-07-17 13:18:42][INFO ][main][o.s.c.c.u.InetUtils.convertAddress:170][]:Cannot determine local hostname
[2025-07-17 13:18:42][INFO ][main][o.a.j.l.DirectJDKLog.log:173][]:Starting ProtocolHandler ["http-nio-9232"]
[2025-07-17 13:18:44][INFO ][main][o.s.b.w.e.t.TomcatWebServer.start:220][]:Tomcat started on port(s): 9232 (http) with context path '/settleReport'
[2025-07-17 13:18:45][INFO ][main][o.s.c.c.u.InetUtils.convertAddress:170][]:Cannot determine local hostname
[2025-07-17 13:18:45][INFO ][main][s.d.s.w.p.DocumentationPluginsBootstrapper.start:93][]:Documentation plugins bootstrapped
[2025-07-17 13:18:45][INFO ][main][s.d.s.w.p.AbstractDocumentationPluginsBootstrapper.bootstrapDocumentationPlugins:79][]:Found 1 custom documentation plugin(s)
[2025-07-17 13:18:46][INFO ][main][s.d.s.w.s.ApiListingReferenceScanner.scan:44][]:Scanning for api listing references
[2025-07-17 13:18:46][INFO ][main][o.s.b.StartupInfoLogger.logStarted:61][]:Started BossServiceSettleApplication in 15.441 seconds (JVM running for 16.754)
[2025-07-17 13:19:39][INFO ][http-nio-9232-exec-1][o.a.j.r.showReport_jsp._jspService:205][]:第一次获取的rKey：10209
[2025-07-17 13:19:39][ERROR][Druid-ConnectionPool-Create-936618636][c.a.d.p.DruidDataSource$CreateConnectionThread.run:2787][]:create connection SQLException, url: jdbc:mysql://*************:2206/stlusers?serverTimezone=UTC&useUnicode=true&characterEncoding=utf-8&zeroDateTimeBehavior=convertToNull&useSSL=false&allowPublicKeyRetrieval=true, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:836)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:456)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:246)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1652)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1718)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2785)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:89)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:144)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:956)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:826)
	... 6 common frames omitted
Caused by: java.net.SocketException: Permission denied: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:155)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	... 9 common frames omitted
[2025-07-17 13:19:39][ERROR][Druid-ConnectionPool-Create-936618636][c.a.d.p.DruidDataSource$CreateConnectionThread.run:2787][]:create connection SQLException, url: jdbc:mysql://*************:2206/stlusers?serverTimezone=UTC&useUnicode=true&characterEncoding=utf-8&zeroDateTimeBehavior=convertToNull&useSSL=false&allowPublicKeyRetrieval=true, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:836)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:456)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:246)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1652)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1718)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2785)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:89)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:144)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:956)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:826)
	... 6 common frames omitted
Caused by: java.net.SocketException: Permission denied: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:155)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	... 9 common frames omitted
[2025-07-17 13:19:39][INFO ][Druid-ConnectionPool-Create-936618636][c.a.d.p.DruidAbstractDataSource.setFailContinuous:1799][]:{dataSource-1} failContinuous is true
[2025-07-17 13:19:40][ERROR][Druid-ConnectionPool-Create-936618636][c.a.d.p.DruidDataSource$CreateConnectionThread.run:2787][]:create connection SQLException, url: jdbc:mysql://*************:2206/stlusers?serverTimezone=UTC&useUnicode=true&characterEncoding=utf-8&zeroDateTimeBehavior=convertToNull&useSSL=false&allowPublicKeyRetrieval=true, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:836)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:456)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:246)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1652)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1718)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2785)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:89)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:144)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:956)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:826)
	... 6 common frames omitted
Caused by: java.net.SocketException: Permission denied: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:155)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	... 9 common frames omitted
[2025-07-17 13:19:40][ERROR][Druid-ConnectionPool-Create-936618636][c.a.d.p.DruidDataSource$CreateConnectionThread.run:2787][]:create connection SQLException, url: jdbc:mysql://*************:2206/stlusers?serverTimezone=UTC&useUnicode=true&characterEncoding=utf-8&zeroDateTimeBehavior=convertToNull&useSSL=false&allowPublicKeyRetrieval=true, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:836)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:456)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:246)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1652)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1718)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2785)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:89)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:144)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:956)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:826)
	... 6 common frames omitted
Caused by: java.net.SocketException: Permission denied: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:155)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	... 9 common frames omitted
[2025-07-17 13:19:41][ERROR][Druid-ConnectionPool-Create-936618636][c.a.d.p.DruidDataSource$CreateConnectionThread.run:2787][]:create connection SQLException, url: jdbc:mysql://*************:2206/stlusers?serverTimezone=UTC&useUnicode=true&characterEncoding=utf-8&zeroDateTimeBehavior=convertToNull&useSSL=false&allowPublicKeyRetrieval=true, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:836)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:456)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:246)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1652)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1718)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2785)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:89)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:144)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:956)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:826)
	... 6 common frames omitted
Caused by: java.net.SocketException: Permission denied: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:155)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	... 9 common frames omitted
[2025-07-17 13:19:41][ERROR][Druid-ConnectionPool-Create-936618636][c.a.d.p.DruidDataSource$CreateConnectionThread.run:2787][]:create connection SQLException, url: jdbc:mysql://*************:2206/stlusers?serverTimezone=UTC&useUnicode=true&characterEncoding=utf-8&zeroDateTimeBehavior=convertToNull&useSSL=false&allowPublicKeyRetrieval=true, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:836)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:456)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:246)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1652)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1718)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2785)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:89)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:144)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:956)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:826)
	... 6 common frames omitted
Caused by: java.net.SocketException: Permission denied: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:155)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	... 9 common frames omitted
[2025-07-17 13:19:42][ERROR][Druid-ConnectionPool-Create-936618636][c.a.d.p.DruidDataSource$CreateConnectionThread.run:2787][]:create connection SQLException, url: jdbc:mysql://*************:2206/stlusers?serverTimezone=UTC&useUnicode=true&characterEncoding=utf-8&zeroDateTimeBehavior=convertToNull&useSSL=false&allowPublicKeyRetrieval=true, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:836)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:456)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:246)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1652)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1718)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2785)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:89)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:144)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:956)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:826)
	... 6 common frames omitted
Caused by: java.net.SocketException: Permission denied: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:155)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	... 9 common frames omitted
[2025-07-17 13:19:42][ERROR][Druid-ConnectionPool-Create-936618636][c.a.d.p.DruidDataSource$CreateConnectionThread.run:2787][]:create connection SQLException, url: jdbc:mysql://*************:2206/stlusers?serverTimezone=UTC&useUnicode=true&characterEncoding=utf-8&zeroDateTimeBehavior=convertToNull&useSSL=false&allowPublicKeyRetrieval=true, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:836)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:456)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:246)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1652)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1718)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2785)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:89)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:144)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:956)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:826)
	... 6 common frames omitted
Caused by: java.net.SocketException: Permission denied: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:155)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	... 9 common frames omitted
[2025-07-17 13:19:43][ERROR][Druid-ConnectionPool-Create-936618636][c.a.d.p.DruidDataSource$CreateConnectionThread.run:2787][]:create connection SQLException, url: jdbc:mysql://*************:2206/stlusers?serverTimezone=UTC&useUnicode=true&characterEncoding=utf-8&zeroDateTimeBehavior=convertToNull&useSSL=false&allowPublicKeyRetrieval=true, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:836)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:456)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:246)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1652)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1718)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2785)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:89)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:144)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:956)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:826)
	... 6 common frames omitted
Caused by: java.net.SocketException: Permission denied: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:155)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	... 9 common frames omitted
[2025-07-17 13:19:44][ERROR][Druid-ConnectionPool-Create-936618636][c.a.d.p.DruidDataSource$CreateConnectionThread.run:2787][]:create connection SQLException, url: jdbc:mysql://*************:2206/stlusers?serverTimezone=UTC&useUnicode=true&characterEncoding=utf-8&zeroDateTimeBehavior=convertToNull&useSSL=false&allowPublicKeyRetrieval=true, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:836)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:456)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:246)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1652)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1718)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2785)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:89)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:144)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:956)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:826)
	... 6 common frames omitted
Caused by: java.net.SocketException: Permission denied: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:155)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	... 9 common frames omitted
[2025-07-17 13:19:44][ERROR][Druid-ConnectionPool-Create-936618636][c.a.d.p.DruidDataSource$CreateConnectionThread.run:2787][]:create connection SQLException, url: jdbc:mysql://*************:2206/stlusers?serverTimezone=UTC&useUnicode=true&characterEncoding=utf-8&zeroDateTimeBehavior=convertToNull&useSSL=false&allowPublicKeyRetrieval=true, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:836)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:456)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:246)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1652)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1718)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2785)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:89)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:144)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:956)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:826)
	... 6 common frames omitted
Caused by: java.net.SocketException: Permission denied: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:155)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	... 9 common frames omitted
[2025-07-17 13:19:45][ERROR][Druid-ConnectionPool-Create-936618636][c.a.d.p.DruidDataSource$CreateConnectionThread.run:2787][]:create connection SQLException, url: jdbc:mysql://*************:2206/stlusers?serverTimezone=UTC&useUnicode=true&characterEncoding=utf-8&zeroDateTimeBehavior=convertToNull&useSSL=false&allowPublicKeyRetrieval=true, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:836)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:456)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:246)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1652)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1718)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2785)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:89)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:144)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:956)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:826)
	... 6 common frames omitted
Caused by: java.net.SocketException: Permission denied: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:155)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	... 9 common frames omitted
[2025-07-17 13:19:45][ERROR][Druid-ConnectionPool-Create-936618636][c.a.d.p.DruidDataSource$CreateConnectionThread.run:2787][]:create connection SQLException, url: jdbc:mysql://*************:2206/stlusers?serverTimezone=UTC&useUnicode=true&characterEncoding=utf-8&zeroDateTimeBehavior=convertToNull&useSSL=false&allowPublicKeyRetrieval=true, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:836)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:456)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:246)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1652)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1718)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2785)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:89)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:144)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:956)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:826)
	... 6 common frames omitted
Caused by: java.net.SocketException: Permission denied: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:155)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	... 9 common frames omitted
[2025-07-17 13:19:46][ERROR][Druid-ConnectionPool-Create-936618636][c.a.d.p.DruidDataSource$CreateConnectionThread.run:2787][]:create connection SQLException, url: jdbc:mysql://*************:2206/stlusers?serverTimezone=UTC&useUnicode=true&characterEncoding=utf-8&zeroDateTimeBehavior=convertToNull&useSSL=false&allowPublicKeyRetrieval=true, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:836)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:456)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:246)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1652)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1718)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2785)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:89)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:144)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:956)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:826)
	... 6 common frames omitted
Caused by: java.net.SocketException: Permission denied: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:155)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	... 9 common frames omitted
[2025-07-17 13:19:46][ERROR][Druid-ConnectionPool-Create-936618636][c.a.d.p.DruidDataSource$CreateConnectionThread.run:2787][]:create connection SQLException, url: jdbc:mysql://*************:2206/stlusers?serverTimezone=UTC&useUnicode=true&characterEncoding=utf-8&zeroDateTimeBehavior=convertToNull&useSSL=false&allowPublicKeyRetrieval=true, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:836)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:456)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:246)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1652)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1718)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2785)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:89)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:144)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:956)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:826)
	... 6 common frames omitted
Caused by: java.net.SocketException: Permission denied: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:155)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	... 9 common frames omitted
[2025-07-17 13:19:47][ERROR][Druid-ConnectionPool-Create-936618636][c.a.d.p.DruidDataSource$CreateConnectionThread.run:2787][]:create connection SQLException, url: jdbc:mysql://*************:2206/stlusers?serverTimezone=UTC&useUnicode=true&characterEncoding=utf-8&zeroDateTimeBehavior=convertToNull&useSSL=false&allowPublicKeyRetrieval=true, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:836)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:456)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:246)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1652)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1718)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2785)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:89)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:144)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:956)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:826)
	... 6 common frames omitted
Caused by: java.net.SocketException: Permission denied: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:155)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	... 9 common frames omitted
[2025-07-17 13:19:47][ERROR][Druid-ConnectionPool-Create-936618636][c.a.d.p.DruidDataSource$CreateConnectionThread.run:2787][]:create connection SQLException, url: jdbc:mysql://*************:2206/stlusers?serverTimezone=UTC&useUnicode=true&characterEncoding=utf-8&zeroDateTimeBehavior=convertToNull&useSSL=false&allowPublicKeyRetrieval=true, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:836)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:456)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:246)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1652)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1718)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2785)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at sun.reflect.GeneratedConstructorAccessor59.newInstance(Unknown Source)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:89)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:144)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:956)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:826)
	... 6 common frames omitted
Caused by: java.net.SocketException: Permission denied: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:155)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	... 9 common frames omitted
[2025-07-17 13:19:48][ERROR][Druid-ConnectionPool-Create-936618636][c.a.d.p.DruidDataSource$CreateConnectionThread.run:2787][]:create connection SQLException, url: jdbc:mysql://*************:2206/stlusers?serverTimezone=UTC&useUnicode=true&characterEncoding=utf-8&zeroDateTimeBehavior=convertToNull&useSSL=false&allowPublicKeyRetrieval=true, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:836)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:456)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:246)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1652)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1718)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2785)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at sun.reflect.GeneratedConstructorAccessor59.newInstance(Unknown Source)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:89)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:144)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:956)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:826)
	... 6 common frames omitted
Caused by: java.net.SocketException: Permission denied: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:155)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	... 9 common frames omitted
[2025-07-17 13:19:48][ERROR][Druid-ConnectionPool-Create-936618636][c.a.d.p.DruidDataSource$CreateConnectionThread.run:2787][]:create connection SQLException, url: jdbc:mysql://*************:2206/stlusers?serverTimezone=UTC&useUnicode=true&characterEncoding=utf-8&zeroDateTimeBehavior=convertToNull&useSSL=false&allowPublicKeyRetrieval=true, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:836)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:456)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:246)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1652)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1718)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2785)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at sun.reflect.GeneratedConstructorAccessor59.newInstance(Unknown Source)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:89)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:144)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:956)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:826)
	... 6 common frames omitted
Caused by: java.net.SocketException: Permission denied: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:155)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	... 9 common frames omitted
[2025-07-17 13:19:49][ERROR][Druid-ConnectionPool-Create-936618636][c.a.d.p.DruidDataSource$CreateConnectionThread.run:2787][]:create connection SQLException, url: jdbc:mysql://*************:2206/stlusers?serverTimezone=UTC&useUnicode=true&characterEncoding=utf-8&zeroDateTimeBehavior=convertToNull&useSSL=false&allowPublicKeyRetrieval=true, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:836)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:456)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:246)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1652)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1718)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2785)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at sun.reflect.GeneratedConstructorAccessor59.newInstance(Unknown Source)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:89)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:144)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:956)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:826)
	... 6 common frames omitted
Caused by: java.net.SocketException: Permission denied: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:155)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	... 9 common frames omitted
[2025-07-17 13:19:49][ERROR][Druid-ConnectionPool-Create-936618636][c.a.d.p.DruidDataSource$CreateConnectionThread.run:2787][]:create connection SQLException, url: jdbc:mysql://*************:2206/stlusers?serverTimezone=UTC&useUnicode=true&characterEncoding=utf-8&zeroDateTimeBehavior=convertToNull&useSSL=false&allowPublicKeyRetrieval=true, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:836)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:456)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:246)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1652)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1718)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2785)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at sun.reflect.GeneratedConstructorAccessor59.newInstance(Unknown Source)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:89)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:144)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:956)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:826)
	... 6 common frames omitted
Caused by: java.net.SocketException: Permission denied: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:155)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	... 9 common frames omitted
[2025-07-17 13:19:50][ERROR][Druid-ConnectionPool-Create-936618636][c.a.d.p.DruidDataSource$CreateConnectionThread.run:2787][]:create connection SQLException, url: jdbc:mysql://*************:2206/stlusers?serverTimezone=UTC&useUnicode=true&characterEncoding=utf-8&zeroDateTimeBehavior=convertToNull&useSSL=false&allowPublicKeyRetrieval=true, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:836)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:456)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:246)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1652)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1718)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2785)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at sun.reflect.GeneratedConstructorAccessor59.newInstance(Unknown Source)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:89)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:144)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:956)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:826)
	... 6 common frames omitted
Caused by: java.net.SocketException: Permission denied: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:155)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	... 9 common frames omitted
[2025-07-17 13:19:50][ERROR][Druid-ConnectionPool-Create-936618636][c.a.d.p.DruidDataSource$CreateConnectionThread.run:2787][]:create connection SQLException, url: jdbc:mysql://*************:2206/stlusers?serverTimezone=UTC&useUnicode=true&characterEncoding=utf-8&zeroDateTimeBehavior=convertToNull&useSSL=false&allowPublicKeyRetrieval=true, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:836)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:456)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:246)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1652)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1718)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2785)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at sun.reflect.GeneratedConstructorAccessor59.newInstance(Unknown Source)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:89)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:144)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:956)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:826)
	... 6 common frames omitted
Caused by: java.net.SocketException: Permission denied: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:155)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	... 9 common frames omitted
[2025-07-17 13:19:51][ERROR][Druid-ConnectionPool-Create-936618636][c.a.d.p.DruidDataSource$CreateConnectionThread.run:2787][]:create connection SQLException, url: jdbc:mysql://*************:2206/stlusers?serverTimezone=UTC&useUnicode=true&characterEncoding=utf-8&zeroDateTimeBehavior=convertToNull&useSSL=false&allowPublicKeyRetrieval=true, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:836)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:456)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:246)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1652)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1718)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2785)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at sun.reflect.GeneratedConstructorAccessor59.newInstance(Unknown Source)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:89)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:144)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:956)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:826)
	... 6 common frames omitted
Caused by: java.net.SocketException: Permission denied: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:155)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	... 9 common frames omitted
[2025-07-17 13:19:51][ERROR][Druid-ConnectionPool-Create-936618636][c.a.d.p.DruidDataSource$CreateConnectionThread.run:2787][]:create connection SQLException, url: jdbc:mysql://*************:2206/stlusers?serverTimezone=UTC&useUnicode=true&characterEncoding=utf-8&zeroDateTimeBehavior=convertToNull&useSSL=false&allowPublicKeyRetrieval=true, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:836)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:456)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:246)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1652)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1718)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2785)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at sun.reflect.GeneratedConstructorAccessor59.newInstance(Unknown Source)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:89)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:144)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:956)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:826)
	... 6 common frames omitted
Caused by: java.net.SocketException: Permission denied: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:155)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	... 9 common frames omitted
[2025-07-17 13:20:14][INFO ][main][o.s.b.SpringApplication.logStartupProfileInfo:637][]:The following 1 profile is active: "dev"
[2025-07-17 13:20:15][INFO ][main][o.s.d.r.c.RepositoryConfigurationDelegate.multipleStoresDetected:262][]:Multiple Spring Data modules found, entering strict repository configuration mode
[2025-07-17 13:20:15][INFO ][main][o.s.d.r.c.RepositoryConfigurationDelegate.registerRepositoriesIn:132][]:Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[2025-07-17 13:20:15][INFO ][main][o.s.d.r.c.RepositoryConfigurationDelegate.registerRepositoriesIn:201][]:Finished Spring Data repository scanning in 15 ms. Found 0 Redis repository interfaces.
[2025-07-17 13:20:15][INFO ][main][o.s.c.c.s.GenericScope.setSerializationId:283][]:BeanFactory id=dbe784e5-818f-3f46-bd86-89a8e46f5497
[2025-07-17 13:20:15][INFO ][main][c.u.j.c.EnableEncryptablePropertiesBeanFactoryPostProcessor.postProcessBeanFactory:39][]:Post-processing PropertySource instances
[2025-07-17 13:20:15][INFO ][main][c.u.j.EncryptablePropertySourceConverter.makeEncryptable:56][]:Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[2025-07-17 13:20:15][INFO ][main][c.u.j.EncryptablePropertySourceConverter.makeEncryptable:56][]:Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[2025-07-17 13:20:15][INFO ][main][c.u.j.EncryptablePropertySourceConverter.makeEncryptable:56][]:Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[2025-07-17 13:20:15][INFO ][main][c.u.j.EncryptablePropertySourceConverter.makeEncryptable:56][]:Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[2025-07-17 13:20:15][INFO ][main][c.u.j.EncryptablePropertySourceConverter.makeEncryptable:56][]:Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[2025-07-17 13:20:15][INFO ][main][c.u.j.EncryptablePropertySourceConverter.makeEncryptable:56][]:Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[2025-07-17 13:20:15][INFO ][main][c.u.j.EncryptablePropertySourceConverter.makeEncryptable:56][]:Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[2025-07-17 13:20:15][INFO ][main][c.u.j.EncryptablePropertySourceConverter.makeEncryptable:56][]:Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[2025-07-17 13:20:15][INFO ][main][c.u.j.EncryptablePropertySourceConverter.makeEncryptable:56][]:Converting PropertySource Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[2025-07-17 13:20:15][INFO ][main][c.u.j.EncryptablePropertySourceConverter.makeEncryptable:56][]:Converting PropertySource Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[2025-07-17 13:20:15][INFO ][main][c.u.j.EncryptablePropertySourceConverter.makeEncryptable:56][]:Converting PropertySource Config resource 'class path resource [application.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[2025-07-17 13:20:15][INFO ][main][c.u.j.EncryptablePropertySourceConverter.makeEncryptable:56][]:Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[2025-07-17 13:20:15][INFO ][main][c.u.j.EncryptablePropertySourceConverter.makeEncryptable:56][]:Converting PropertySource defaultProperties [org.springframework.boot.DefaultPropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[2025-07-17 13:20:15][INFO ][main][c.u.j.f.DefaultLazyPropertyFilter.lambda$null$2:31][]:Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[2025-07-17 13:20:16][INFO ][main][c.u.j.r.DefaultLazyPropertyResolver.lambda$null$2:35][]:Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[2025-07-17 13:20:16][INFO ][main][c.u.j.d.DefaultLazyPropertyDetector.lambda$null$2:35][]:Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[2025-07-17 13:20:16][DEBUG][main][o.s.b.w.s.s.DocumentRoot.getArchiveFileDocumentRoot:81][]:Code archive: D:\04server\apache-maven-3.6.2\repository3\org\springframework\boot\spring-boot\2.7.6\spring-boot-2.7.6.jar
[2025-07-17 13:20:16][DEBUG][main][o.s.b.w.s.s.DocumentRoot.getExplodedWarFileDocumentRoot:125][]:Code archive: D:\04server\apache-maven-3.6.2\repository3\org\springframework\boot\spring-boot\2.7.6\spring-boot-2.7.6.jar
[2025-07-17 13:20:16][DEBUG][main][o.s.b.w.s.s.DocumentRoot.getValidDirectory:69][]:Document root: D:\05code\newsettle\settle-service-report\src\main\webapp
[2025-07-17 13:20:16][INFO ][main][o.s.b.w.e.t.TomcatWebServer.initialize:108][]:Tomcat initialized with port(s): 9232 (http)
[2025-07-17 13:20:16][INFO ][main][o.a.j.l.DirectJDKLog.log:173][]:Initializing ProtocolHandler ["http-nio-9232"]
[2025-07-17 13:20:16][INFO ][main][o.a.j.l.DirectJDKLog.log:173][]:Starting service [Tomcat]
[2025-07-17 13:20:16][INFO ][main][o.a.j.l.DirectJDKLog.log:173][]:Starting Servlet engine: [Apache Tomcat/9.0.69]
[2025-07-17 13:20:16][INFO ][main][o.a.j.l.DirectJDKLog.log:173][]:At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
[2025-07-17 13:20:16][INFO ][main][o.a.j.l.DirectJDKLog.log:173][]:Initializing Spring embedded WebApplicationContext
[2025-07-17 13:20:16][INFO ][main][o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:292][]:Root WebApplicationContext: initialization completed in 1972 ms
[2025-07-17 13:20:16][INFO ][main][c.h.s.c.ReportWebConfig.servletRegistrationBean:30][]:NACOS_SERVER:null
[2025-07-17 13:20:16][INFO ][main][c.h.s.c.ReportWebConfig.servletRegistrationBean:38][]:开发环境部署-配置文件：raqsoftConfig.xml
[2025-07-17 13:20:17][INFO ][main][c.a.d.s.b.a.DruidDataSourceAutoConfigure.dataSource:56][]:Init DruidDataSource
[2025-07-17 13:20:17][INFO ][main][c.a.d.p.DruidDataSource.init:985][]:{dataSource-1} inited
[2025-07-17 13:20:19][INFO ][main][c.h.c.b.s.c.r.RedisPoolConfiguration.redisPoolConfig:279][]:redis 连接池配置
[2025-07-17 13:20:19][INFO ][main][c.h.c.b.s.c.r.RedisPoolConfiguration.redisConnectionFactory:94][]:redis factory 加载
[2025-07-17 13:20:19][INFO ][main][c.h.c.b.s.c.r.RedisPoolConfiguration.getRedisStandaloneConfiguration:230][]:redis 单机配置
[2025-07-17 13:20:19][WARN ][main][c.b.m.c.m.TableInfoHelper.initTableFields:342][]:Can not find table primary key in Class: "com.hp.settle.entity.ReportGoldConfig".
[2025-07-17 13:20:19][WARN ][main][c.b.m.c.i.DefaultSqlInjector.getMethodList:56][]:class com.hp.settle.entity.ReportGoldConfig ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[2025-07-17 13:20:20][INFO ][main][c.h.c.b.s.c.r.RedisConfig.redisTemplate:29][]:使用lettuce连接
[2025-07-17 13:20:21][INFO ][main][s.d.s.w.WebMvcPropertySourcedRequestMappingHandlerMapping.initHandlerMethods:69][]:Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
[2025-07-17 13:20:23][INFO ][main][o.s.c.c.u.InetUtils.convertAddress:170][]:Cannot determine local hostname
[2025-07-17 13:20:23][INFO ][main][o.a.j.l.DirectJDKLog.log:173][]:Starting ProtocolHandler ["http-nio-9232"]
[2025-07-17 13:20:24][INFO ][main][o.s.b.w.e.t.TomcatWebServer.start:220][]:Tomcat started on port(s): 9232 (http) with context path '/settleReport'
[2025-07-17 13:20:26][INFO ][main][o.s.c.c.u.InetUtils.convertAddress:170][]:Cannot determine local hostname
[2025-07-17 13:20:26][INFO ][main][s.d.s.w.p.DocumentationPluginsBootstrapper.start:93][]:Documentation plugins bootstrapped
[2025-07-17 13:20:26][INFO ][main][s.d.s.w.p.AbstractDocumentationPluginsBootstrapper.bootstrapDocumentationPlugins:79][]:Found 1 custom documentation plugin(s)
[2025-07-17 13:20:26][INFO ][main][s.d.s.w.s.ApiListingReferenceScanner.scan:44][]:Scanning for api listing references
[2025-07-17 13:20:26][INFO ][main][o.s.b.StartupInfoLogger.logStarted:61][]:Started BossServiceSettleApplication in 16.153 seconds (JVM running for 17.332)
[2025-07-17 13:20:31][INFO ][http-nio-9232-exec-1][o.a.j.r.showReport_jsp._jspService:205][]:第一次获取的rKey：10209
[2025-07-17 13:20:33][DEBUG][http-nio-9232-exec-1][o.a.i.l.j.BaseJdbcLogger.debug:137][]:==>  Preparing: select * from STLUDR.RVL_REPORT_RAQ where R_KEY = ? order by R_ID desc
[2025-07-17 13:20:33][DEBUG][http-nio-9232-exec-1][o.a.i.l.j.BaseJdbcLogger.debug:137][]:==> Parameters: 10209(String)
[2025-07-17 13:20:33][DEBUG][http-nio-9232-exec-1][o.a.i.l.j.BaseJdbcLogger.debug:137][]:<==      Total: 1
[2025-07-17 13:20:33][INFO ][http-nio-9232-exec-1][c.h.s.u.ReportUtil.getShowReportJspUrl:62][]:modifiedRpx: other/Sk_Line_Supp_srv6-3.rpx
[2025-07-17 13:20:33][DEBUG][http-nio-9232-exec-1][c.h.s.u.ReportUtil.getExcelName:107][]:-----------getExcelName()
[2025-07-17 13:20:33][DEBUG][http-nio-9232-exec-1][c.h.s.u.ReportUtil.getExcelName:108][]:rKey=10209
[2025-07-17 13:20:33][DEBUG][http-nio-9232-exec-1][c.h.s.u.ReportUtil.getExcelName:113][]:ce.getRaqName=双跨专线结算补充报表（SRv6-VPN智能专线）ADJUST-落地方分表
[2025-07-17 13:20:33][DEBUG][http-nio-9232-exec-1][o.a.i.l.j.BaseJdbcLogger.debug:137][]:==>  Preparing: SELECT T.DICTDESC FROM STLUDR.STL_CONF_DICT T WHERE T.DICTVALUE = ? AND T.ITEM = 'ENTITY'
[2025-07-17 13:20:33][DEBUG][http-nio-9232-exec-1][o.a.i.l.j.BaseJdbcLogger.debug:137][]:==> Parameters: null
[2025-07-17 13:20:33][DEBUG][http-nio-9232-exec-1][o.a.i.l.j.BaseJdbcLogger.debug:137][]:<==      Total: 0
[2025-07-17 13:20:33][DEBUG][http-nio-9232-exec-1][c.h.s.u.ReportUtil.getExcelName:130][]:settlemonth=202507
[2025-07-17 13:20:33][DEBUG][http-nio-9232-exec-1][c.h.s.u.ReportUtil.getExcelName:131][]:taxrate=6
[2025-07-17 13:20:33][DEBUG][http-nio-9232-exec-1][c.h.s.u.ReportUtil.getExcelName:132][]:entity=全部
[2025-07-17 13:20:33][DEBUG][http-nio-9232-exec-1][c.h.s.u.ReportUtil.getExcelName:133][]:income=业务总收入
[2025-07-17 13:20:33][DEBUG][http-nio-9232-exec-1][c.h.s.u.ReportUtil.getExcelName:134][]:in_out=结入
[2025-07-17 13:20:33][DEBUG][http-nio-9232-exec-1][c.h.s.u.ReportUtil.getExcelName:135][]:in_out=结入
[2025-07-17 13:20:33][INFO ][http-nio-9232-exec-1][c.h.s.u.ReportUtil.getExcelName:187][]:let me look look : 双跨专线结算补充报表（SRv6-VPN智能专线）-落地方分表
[2025-07-17 13:20:33][INFO ][http-nio-9232-exec-1][o.a.j.r.showReport_jsp._jspService:209][]:report:other/Sk_Line_Supp_srv6-3.rpx
[2025-07-17 13:20:33][INFO ][http-nio-9232-exec-1][o.a.j.r.showReport_jsp._jspService:210][]:excelName:双跨专线结算补充报表（SRv6-VPN智能专线）-落地方分表
[2025-07-17 13:20:33][INFO ][http-nio-9232-exec-1][o.a.j.r.showReport_jsp._jspService:237][]:请求参数：R_KEY=10209;tree_node_id=1068;staffNum=85195550005;
[2025-07-17 13:20:33][INFO ][http-nio-9232-exec-1][o.a.j.r.showReport_jsp._jspService:241][]:other/Sk_Line_Supp_srv6-3.rpx，是否有参数：true,参数报表：other/Sk_Line_Supp_srv6-3_arg.rpx
[2025-07-17 13:20:33][INFO ][http-nio-9232-exec-1][o.a.j.l.DirectJDKLog.log:173][]:Character decoding failed. Parameter [scale] with value [<%=scale %>] has been ignored. Note that the name and value quoted here may be corrupted due to the failed decoding. Use debug level logging to see the original, non-corrupted values.
 Note: further occurrences of Parameter errors will be logged at DEBUG level.
[2025-07-17 13:20:33][DEBUG][http-nio-9232-exec-1][o.a.i.l.j.BaseJdbcLogger.debug:137][]:==>  Preparing: select * from STLUDR.RVL_REPORT_RAQ where R_KEY = ? order by R_ID desc
[2025-07-17 13:20:33][DEBUG][http-nio-9232-exec-1][o.a.i.l.j.BaseJdbcLogger.debug:137][]:==> Parameters: 10209(String)
[2025-07-17 13:20:33][DEBUG][http-nio-9232-exec-1][o.a.i.l.j.BaseJdbcLogger.debug:137][]:<==      Total: 1
[2025-07-17 13:20:33][INFO ][http-nio-9232-exec-1][c.h.s.u.ReportUtil.getShowReportJspUrl:62][]:modifiedRpx: other/Sk_Line_Supp_srv6-3.rpx
[2025-07-17 13:20:34][INFO ][http-nio-9232-exec-9][o.a.j.l.DirectJDKLog.log:173][]:Initializing Spring DispatcherServlet 'dispatcherServlet'
[2025-07-17 13:20:34][INFO ][http-nio-9232-exec-9][o.s.w.s.FrameworkServlet.initServletBean:525][]:Initializing Servlet 'dispatcherServlet'
[2025-07-17 13:20:34][INFO ][http-nio-9232-exec-9][o.s.w.s.FrameworkServlet.initServletBean:547][]:Completed initialization in 6 ms
[2025-07-17 13:20:34][INFO ][http-nio-9232-exec-6][c.h.s.c.AuthenticationFilter.doFilter:101][]:进入放行路径requestURLhttp://127.0.0.1:9232/settleReport/raqsoft/easyui/jquery.min.js
[2025-07-17 13:20:34][INFO ][http-nio-9232-exec-7][c.h.s.c.AuthenticationFilter.doFilter:101][]:进入放行路径requestURLhttp://127.0.0.1:9232/settleReport/raqsoft/easyui/jquery.easyui.min.js
[2025-07-17 13:20:34][INFO ][http-nio-9232-exec-9][c.h.s.c.AuthenticationFilter.doFilter:101][]:进入放行路径requestURLhttp://127.0.0.1:9232/settleReport/raqsoft/easyui/themes/icon.css
[2025-07-17 13:20:34][INFO ][http-nio-9232-exec-4][c.h.s.c.AuthenticationFilter.doFilter:101][]:进入放行路径requestURLhttp://127.0.0.1:9232/settleReport/raqsoft/easyui/themes/default/easyui.css
[2025-07-17 13:20:34][INFO ][http-nio-9232-exec-3][c.h.s.c.AuthenticationFilter.doFilter:101][]:进入放行路径requestURLhttp://127.0.0.1:9232/settleReport/reportJsp/css/style.css
[2025-07-17 13:20:34][INFO ][http-nio-9232-exec-4][c.h.s.c.AuthenticationFilter.doFilter:101][]:进入放行路径requestURLhttp://127.0.0.1:9232/settleReport/raqsoft/echarts/ecStat.min.js
[2025-07-17 13:20:34][INFO ][http-nio-9232-exec-9][c.h.s.c.AuthenticationFilter.doFilter:101][]:进入放行路径requestURLhttp://127.0.0.1:9232/settleReport/raqsoft/easyui/locale/easyui-lang-zh_CN.js
[2025-07-17 13:20:34][INFO ][http-nio-9232-exec-3][c.h.s.c.AuthenticationFilter.doFilter:101][]:进入放行路径requestURLhttp://127.0.0.1:9232/settleReport/raqsoft/echarts/echarts.js
[2025-07-17 13:20:34][INFO ][http-nio-9232-exec-8][c.h.s.c.AuthenticationFilter.doFilter:101][]:进入放行路径requestURLhttp://127.0.0.1:9232/settleReport/raqsoft/echarts/echarts-gl.min.js
[2025-07-17 13:20:34][INFO ][http-nio-9232-exec-2][c.h.s.c.AuthenticationFilter.doFilter:101][]:进入放行路径requestURLhttp://127.0.0.1:9232/settleReport/raqsoft/echarts/d3.v5.min.js
[2025-07-17 13:20:34][INFO ][http-nio-9232-exec-5][c.h.s.c.AuthenticationFilter.doFilter:101][]:进入放行路径requestURLhttp://127.0.0.1:9232/settleReport/raqsoft/echarts3/map/js/china.js
[2025-07-17 13:20:34][INFO ][http-nio-9232-exec-10][c.h.s.c.AuthenticationFilter.doFilter:101][]:进入放行路径requestURLhttp://127.0.0.1:9232/settleReport/raqsoft/echarts3/dist/extension/dataTool.min.js
[2025-07-17 13:20:34][INFO ][http-nio-9232-exec-9][c.h.s.c.AuthenticationFilter.doFilter:101][]:进入放行路径requestURLhttp://127.0.0.1:9232/settleReport/raqsoft/echarts3/map/js/world.js
[2025-07-17 13:20:34][INFO ][http-nio-9232-exec-6][c.h.s.c.AuthenticationFilter.doFilter:101][]:进入放行路径requestURLhttp://127.0.0.1:9232/settleReport/raqsoft/echarts3/dist/extension/bmap.min.js
[2025-07-17 13:20:34][INFO ][http-nio-9232-exec-7][c.h.s.c.AuthenticationFilter.doFilter:101][]:进入放行路径requestURLhttp://127.0.0.1:9232/settleReport/raqsoft/echarts2/dist/echarts.js
[2025-07-17 13:20:34][INFO ][http-nio-9232-exec-4][c.h.s.c.AuthenticationFilter.doFilter:101][]:进入放行路径requestURLhttp://127.0.0.1:9232/settleReport/raqsoft/images/loading.gif
[2025-07-17 13:20:34][INFO ][http-nio-9232-exec-10][c.h.s.c.AuthenticationFilter.doFilter:101][]:进入放行路径requestURLhttp://127.0.0.1:9232/settleReport/reportJsp/js/jquery.blcokui.js
[2025-07-17 13:20:34][INFO ][http-nio-9232-exec-5][c.h.s.c.AuthenticationFilter.doFilter:101][]:进入放行路径requestURLhttp://127.0.0.1:9232/settleReport/raqsoft/pdfjs/toast/javascript/jquery.toastmessage.js
[2025-07-17 13:20:34][INFO ][http-nio-9232-exec-6][c.h.s.c.AuthenticationFilter.doFilter:101][]:进入放行路径requestURLhttp://127.0.0.1:9232/settleReport/raqsoft/pdfjs/toast/resources/css/jquery.toastmessage.css
[2025-07-17 13:20:41][INFO ][http-nio-9232-exec-8][c.h.s.c.AuthenticationFilter.doFilter:101][]:进入放行路径requestURLhttp://127.0.0.1:9232/settleReport/reportServlet
[2025-07-17 13:20:43][INFO ][http-nio-9232-exec-4][c.h.s.c.AuthenticationFilter.doFilter:101][]:进入放行路径requestURLhttp://127.0.0.1:9232/settleReport/reportJsp/images/btnBarBg.jpg
[2025-07-17 13:20:43][INFO ][http-nio-9232-exec-9][c.h.s.c.AuthenticationFilter.doFilter:101][]:进入放行路径requestURLhttp://127.0.0.1:9232/settleReport/reportJsp/images/localBg.jpg
[2025-07-17 13:20:43][INFO ][http-nio-9232-exec-5][c.h.s.c.AuthenticationFilter.doFilter:101][]:进入放行路径requestURLhttp://127.0.0.1:9232/settleReport/reportJsp/images/ViewReport-ICO.gif
[2025-07-17 13:20:43][INFO ][http-nio-9232-exec-10][c.h.s.c.AuthenticationFilter.doFilter:101][]:进入放行路径requestURLhttp://127.0.0.1:9232/settleReport/reportJsp/images/print.png
[2025-07-17 13:20:43][INFO ][http-nio-9232-exec-6][c.h.s.c.AuthenticationFilter.doFilter:101][]:进入放行路径requestURLhttp://127.0.0.1:9232/settleReport/raqsoft/easyui/themes/default/images/combo_arrow.png
[2025-07-17 13:20:43][INFO ][http-nio-9232-exec-2][c.h.s.c.AuthenticationFilter.doFilter:101][]:进入放行路径requestURLhttp://127.0.0.1:9232/settleReport/raqsoft/easyui/themes/default/images/datebox_arrow.png
[2025-07-17 13:20:43][INFO ][http-nio-9232-exec-7][c.h.s.c.AuthenticationFilter.doFilter:101][]:进入放行路径requestURLhttp://127.0.0.1:9232/settleReport/reportServlet
[2025-07-17 13:20:43][INFO ][http-nio-9232-exec-7][c.h.s.r.ReportParamPostHandle.process:19][]:paramName:settlemonth---paramValue:202507
[2025-07-17 13:20:43][INFO ][http-nio-9232-exec-3][c.h.s.c.AuthenticationFilter.doFilter:159][]:进入 /reportJsp/queryReport.jsp
[2025-07-17 13:20:43][INFO ][http-nio-9232-exec-3][o.a.j.r.queryReport_jsp._jspService:169][]:context-path:/settleReport
[2025-07-17 13:20:43][INFO ][http-nio-9232-exec-3][o.a.j.r.queryReport_jsp._jspService:171][]:第3次获取的rKey：10209
[2025-07-17 13:20:43][INFO ][http-nio-9232-exec-3][o.a.j.r.queryReport_jsp._jspService:173][]:第4次获取的rKey1：10209
[2025-07-17 13:20:43][INFO ][http-nio-9232-exec-3][o.a.j.r.queryReport_jsp._jspService:179][]:联动报表4进来了
[2025-07-17 13:20:43][INFO ][http-nio-9232-exec-3][o.a.j.r.queryReport_jsp._jspService:181][]:联动报表4进来了获取的rKey：10209
[2025-07-17 13:20:43][INFO ][http-nio-9232-exec-3][o.a.j.r.queryReport_jsp._jspService:187][]:settlemonth:202507
[2025-07-17 13:20:43][DEBUG][http-nio-9232-exec-3][o.a.i.l.j.BaseJdbcLogger.debug:137][]:==>  Preparing: select * from STLUDR.RVL_REPORT_RAQ where R_KEY = ? and ? between START_PAYMENT and END_PAYMENT order by R_ID desc limit 1
[2025-07-17 13:20:43][DEBUG][http-nio-9232-exec-3][o.a.i.l.j.BaseJdbcLogger.debug:137][]:==> Parameters: 10209(String), 202507(String)
[2025-07-17 13:20:43][DEBUG][http-nio-9232-exec-3][o.a.i.l.j.BaseJdbcLogger.debug:137][]:<==      Total: 1
[2025-07-17 13:20:43][DEBUG][http-nio-9232-exec-3][c.h.s.u.ReportUtil.getExcelName:107][]:-----------getExcelName()
[2025-07-17 13:20:43][DEBUG][http-nio-9232-exec-3][c.h.s.u.ReportUtil.getExcelName:108][]:rKey=10209
[2025-07-17 13:20:43][DEBUG][http-nio-9232-exec-3][c.h.s.u.ReportUtil.getExcelName:113][]:ce.getRaqName=双跨专线结算补充报表（SRv6-VPN智能专线）ADJUST-落地方分表
[2025-07-17 13:20:43][DEBUG][http-nio-9232-exec-3][o.a.i.l.j.BaseJdbcLogger.debug:137][]:==>  Preparing: SELECT T.DICTDESC FROM STLUDR.STL_CONF_DICT T WHERE T.DICTVALUE = ? AND T.ITEM = 'ENTITY'
[2025-07-17 13:20:43][DEBUG][http-nio-9232-exec-3][o.a.i.l.j.BaseJdbcLogger.debug:137][]:==> Parameters: null
[2025-07-17 13:20:43][DEBUG][http-nio-9232-exec-3][o.a.i.l.j.BaseJdbcLogger.debug:137][]:<==      Total: 0
[2025-07-17 13:20:43][DEBUG][http-nio-9232-exec-3][c.h.s.u.ReportUtil.getExcelName:130][]:settlemonth=202507
[2025-07-17 13:20:43][DEBUG][http-nio-9232-exec-3][c.h.s.u.ReportUtil.getExcelName:131][]:taxrate=6
[2025-07-17 13:20:43][DEBUG][http-nio-9232-exec-3][c.h.s.u.ReportUtil.getExcelName:132][]:entity=全部
[2025-07-17 13:20:43][DEBUG][http-nio-9232-exec-3][c.h.s.u.ReportUtil.getExcelName:133][]:income=业务总收入
[2025-07-17 13:20:43][DEBUG][http-nio-9232-exec-3][c.h.s.u.ReportUtil.getExcelName:134][]:in_out=结入
[2025-07-17 13:20:43][DEBUG][http-nio-9232-exec-3][c.h.s.u.ReportUtil.getExcelName:135][]:in_out=结入
[2025-07-17 13:20:43][DEBUG][http-nio-9232-exec-3][o.a.i.l.j.BaseJdbcLogger.debug:137][]:==>  Preparing: SELECT t.prov_nm FROM stludr.stl_province_cd t WHERE t.prov_cd= ? limit 1
[2025-07-17 13:20:43][DEBUG][http-nio-9232-exec-3][o.a.i.l.j.BaseJdbcLogger.debug:137][]:==> Parameters: 100(String)
[2025-07-17 13:20:43][DEBUG][http-nio-9232-exec-3][o.a.i.l.j.BaseJdbcLogger.debug:137][]:<==      Total: 1
[2025-07-17 13:20:43][INFO ][http-nio-9232-exec-3][c.h.s.u.ReportUtil.getExcelName:187][]:let me look look : 双跨专线结算补充报表（SRv6-VPN智能专线）-落地方分表
[2025-07-17 13:20:43][INFO ][http-nio-9232-exec-3][o.a.j.r.queryReport_jsp._jspService:194][]:从show获取到得report:other/Sk_Line_Supp_srv6-3.rpx
[2025-07-17 13:20:43][INFO ][http-nio-9232-exec-3][o.a.j.r.queryReport_jsp._jspService:201][]:报表名称：双跨专线结算补充报表（SRv6-VPN智能专线）-落地方分表，报表路径：other/Sk_Line_Supp_srv6-3.rpx，是否使用缓存:no
[2025-07-17 13:20:44][DEBUG][http-nio-9232-exec-3][o.a.i.l.j.BaseJdbcLogger.debug:137][]:==>  Preparing: SELECT decode(count(1), 0, 'N', 'Y') AS dictvalue, '' AS dictdesc FROM stludr.am_tree_check_new a WHERE a.TREE_NODE_ID = ? AND a.STATE = 'Y' AND a.ACCT_MONTH = ?
[2025-07-17 13:20:44][DEBUG][http-nio-9232-exec-3][o.a.i.l.j.BaseJdbcLogger.debug:137][]:==> Parameters: 1068(String), 202507(String)
[2025-07-17 13:20:44][DEBUG][http-nio-9232-exec-3][o.a.i.l.j.BaseJdbcLogger.debug:137][]:<==      Total: 1
[2025-07-17 13:20:44][INFO ][http-nio-9232-exec-3][c.h.s.s.i.ReportCheckServiceImpl.isCheck:69][]:******报表审核通过，本报表的编号=[1068]******
[2025-07-17 13:20:44][DEBUG][http-nio-9232-exec-3][o.a.i.l.j.BaseJdbcLogger.debug:137][]:==>  Preparing: SELECT RAQ_KEY,APPROVE_FLAG,GOLD_BANK_CODE FROM report_gold_config WHERE (APPROVE_FLAG = ? AND RAQ_KEY = ?)
[2025-07-17 13:20:44][DEBUG][http-nio-9232-exec-3][o.a.i.l.j.BaseJdbcLogger.debug:137][]:==> Parameters: Y(String), 10209(String)
[2025-07-17 13:20:44][ERROR][http-nio-9232-exec-3][o.a.j.l.DirectJDKLog.log:175][]:Servlet.service() for servlet [jsp] threw exception
java.sql.SQLSyntaxErrorException: Unknown column 'GOLD_BANK_CODE' in 'field list'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:120)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:370)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:497)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59)
	at com.sun.proxy.$Proxy142.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:64)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:79)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:63)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:89)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	at com.sun.proxy.$Proxy93.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:166)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy118.selectList(Unknown Source)
	at com.baomidou.mybatisplus.core.mapper.BaseMapper.selectOne(BaseMapper.java:173)
	at java.lang.invoke.MethodHandle.invokeWithArguments(MethodHandle.java:627)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$DefaultMethodInvoker.invoke(MybatisMapperProxy.java:162)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy118.selectOne(Unknown Source)
	at com.baomidou.mybatisplus.extension.service.impl.ServiceImpl.getOne(ServiceImpl.java:202)
	at com.baomidou.mybatisplus.extension.service.IService.getOne(IService.java:320)
	at com.hp.settle.service.impl.ReportGoldApproveImpl.isApprove(ReportGoldApproveImpl.java:49)
	at com.hp.settle.service.impl.ReportGoldApproveImpl$$FastClassBySpringCGLIB$$baea858e.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386)
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704)
	at com.hp.settle.service.impl.ReportGoldApproveImpl$$EnhancerBySpringCGLIB$$d313e785.isApprove(<generated>)
	at com.hp.settle.util.ReportUtil.isApprove(ReportUtil.java:406)
	at org.apache.jsp.reportJsp.queryReport_jsp._jspService(queryReport_jsp.java:218)
	at org.apache.jasper.runtime.HttpJspBase.service(HttpJspBase.java:70)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:750)
	at org.apache.jasper.servlet.JspServletWrapper.service(JspServletWrapper.java:466)
	at org.apache.jasper.servlet.JspServlet.serviceJspFile(JspServlet.java:379)
	at org.apache.jasper.servlet.JspServlet.service(JspServlet.java:327)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:750)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.ApplicationDispatcher.invoke(ApplicationDispatcher.java:711)
	at org.apache.catalina.core.ApplicationDispatcher.processRequest(ApplicationDispatcher.java:459)
	at org.apache.catalina.core.ApplicationDispatcher.doForward(ApplicationDispatcher.java:385)
	at org.apache.catalina.core.ApplicationDispatcher.forward(ApplicationDispatcher.java:313)
	at com.hp.settle.config.AuthenticationFilter.doFilter(AuthenticationFilter.java:160)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:177)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:891)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1784)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:745)
[2025-07-17 13:20:44][ERROR][http-nio-9232-exec-3][o.a.j.l.DirectJDKLog.log:175][]:Servlet.service() for servlet [jsp] in context with path [/settleReport] threw exception [org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column 'GOLD_BANK_CODE' in 'field list'
### The error may exist in com/hp/settle/mapper/ReportGoldApproveMapper.java (best guess)
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT  RAQ_KEY,APPROVE_FLAG,GOLD_BANK_CODE  FROM report_gold_config     WHERE (APPROVE_FLAG = ? AND RAQ_KEY = ?)
### Cause: java.sql.SQLSyntaxErrorException: Unknown column 'GOLD_BANK_CODE' in 'field list'
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'GOLD_BANK_CODE' in 'field list'] with root cause
java.sql.SQLSyntaxErrorException: Unknown column 'GOLD_BANK_CODE' in 'field list'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:120)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:370)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:497)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59)
	at com.sun.proxy.$Proxy142.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:64)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:79)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:63)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:89)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	at com.sun.proxy.$Proxy93.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:166)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy118.selectList(Unknown Source)
	at com.baomidou.mybatisplus.core.mapper.BaseMapper.selectOne(BaseMapper.java:173)
	at java.lang.invoke.MethodHandle.invokeWithArguments(MethodHandle.java:627)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$DefaultMethodInvoker.invoke(MybatisMapperProxy.java:162)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy118.selectOne(Unknown Source)
	at com.baomidou.mybatisplus.extension.service.impl.ServiceImpl.getOne(ServiceImpl.java:202)
	at com.baomidou.mybatisplus.extension.service.IService.getOne(IService.java:320)
	at com.hp.settle.service.impl.ReportGoldApproveImpl.isApprove(ReportGoldApproveImpl.java:49)
	at com.hp.settle.service.impl.ReportGoldApproveImpl$$FastClassBySpringCGLIB$$baea858e.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386)
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704)
	at com.hp.settle.service.impl.ReportGoldApproveImpl$$EnhancerBySpringCGLIB$$d313e785.isApprove(<generated>)
	at com.hp.settle.util.ReportUtil.isApprove(ReportUtil.java:406)
	at org.apache.jsp.reportJsp.queryReport_jsp._jspService(queryReport_jsp.java:218)
	at org.apache.jasper.runtime.HttpJspBase.service(HttpJspBase.java:70)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:750)
	at org.apache.jasper.servlet.JspServletWrapper.service(JspServletWrapper.java:466)
	at org.apache.jasper.servlet.JspServlet.serviceJspFile(JspServlet.java:379)
	at org.apache.jasper.servlet.JspServlet.service(JspServlet.java:327)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:750)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.ApplicationDispatcher.invoke(ApplicationDispatcher.java:711)
	at org.apache.catalina.core.ApplicationDispatcher.processRequest(ApplicationDispatcher.java:459)
	at org.apache.catalina.core.ApplicationDispatcher.doForward(ApplicationDispatcher.java:385)
	at org.apache.catalina.core.ApplicationDispatcher.forward(ApplicationDispatcher.java:313)
	at com.hp.settle.config.AuthenticationFilter.doFilter(AuthenticationFilter.java:160)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:177)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:891)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1784)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:745)
[2025-07-17 13:24:02][INFO ][http-nio-9232-exec-6][c.h.s.c.AuthenticationFilter.doFilter:101][]:进入放行路径requestURLhttp://127.0.0.1:9232/settleReport/reportServlet
[2025-07-17 13:24:02][INFO ][http-nio-9232-exec-6][c.h.s.r.ReportParamPostHandle.process:19][]:paramName:settlemonth---paramValue:202507
[2025-07-17 13:24:02][INFO ][http-nio-9232-exec-2][c.h.s.c.AuthenticationFilter.doFilter:159][]:进入 /reportJsp/queryReport.jsp
[2025-07-17 13:24:02][INFO ][http-nio-9232-exec-2][o.a.j.r.queryReport_jsp._jspService:169][]:context-path:/settleReport
[2025-07-17 13:24:02][INFO ][http-nio-9232-exec-2][o.a.j.r.queryReport_jsp._jspService:171][]:第3次获取的rKey：10209
[2025-07-17 13:24:02][INFO ][http-nio-9232-exec-2][o.a.j.r.queryReport_jsp._jspService:173][]:第4次获取的rKey1：10209
[2025-07-17 13:24:02][INFO ][http-nio-9232-exec-2][o.a.j.r.queryReport_jsp._jspService:179][]:联动报表4进来了
[2025-07-17 13:24:02][INFO ][http-nio-9232-exec-2][o.a.j.r.queryReport_jsp._jspService:181][]:联动报表4进来了获取的rKey：10209
[2025-07-17 13:24:02][INFO ][http-nio-9232-exec-2][o.a.j.r.queryReport_jsp._jspService:187][]:settlemonth:202507
[2025-07-17 13:24:02][WARN ][http-nio-9232-exec-2][c.a.d.p.DruidAbstractDataSource.testConnectionInternal:1489][]:discard long time none received connection. , jdbcUrl : ****************************************************************************************************************************************************************************, version : 1.2.4, lastPacketReceivedIdleMillis : 197736
[2025-07-17 13:24:03][DEBUG][http-nio-9232-exec-2][o.a.i.l.j.BaseJdbcLogger.debug:137][]:==>  Preparing: select * from STLUDR.RVL_REPORT_RAQ where R_KEY = ? and ? between START_PAYMENT and END_PAYMENT order by R_ID desc limit 1
[2025-07-17 13:24:03][DEBUG][http-nio-9232-exec-2][o.a.i.l.j.BaseJdbcLogger.debug:137][]:==> Parameters: 10209(String), 202507(String)
[2025-07-17 13:24:03][DEBUG][http-nio-9232-exec-2][o.a.i.l.j.BaseJdbcLogger.debug:137][]:<==      Total: 1
[2025-07-17 13:24:03][DEBUG][http-nio-9232-exec-2][c.h.s.u.ReportUtil.getExcelName:107][]:-----------getExcelName()
[2025-07-17 13:24:03][DEBUG][http-nio-9232-exec-2][c.h.s.u.ReportUtil.getExcelName:108][]:rKey=10209
[2025-07-17 13:24:03][DEBUG][http-nio-9232-exec-2][c.h.s.u.ReportUtil.getExcelName:113][]:ce.getRaqName=双跨专线结算补充报表（SRv6-VPN智能专线）ADJUST-落地方分表
[2025-07-17 13:24:03][DEBUG][http-nio-9232-exec-2][o.a.i.l.j.BaseJdbcLogger.debug:137][]:==>  Preparing: SELECT T.DICTDESC FROM STLUDR.STL_CONF_DICT T WHERE T.DICTVALUE = ? AND T.ITEM = 'ENTITY'
[2025-07-17 13:24:03][DEBUG][http-nio-9232-exec-2][o.a.i.l.j.BaseJdbcLogger.debug:137][]:==> Parameters: null
[2025-07-17 13:24:03][DEBUG][http-nio-9232-exec-2][o.a.i.l.j.BaseJdbcLogger.debug:137][]:<==      Total: 0
[2025-07-17 13:24:03][DEBUG][http-nio-9232-exec-2][c.h.s.u.ReportUtil.getExcelName:130][]:settlemonth=202507
[2025-07-17 13:24:03][DEBUG][http-nio-9232-exec-2][c.h.s.u.ReportUtil.getExcelName:131][]:taxrate=6
[2025-07-17 13:24:03][DEBUG][http-nio-9232-exec-2][c.h.s.u.ReportUtil.getExcelName:132][]:entity=全部
[2025-07-17 13:24:03][DEBUG][http-nio-9232-exec-2][c.h.s.u.ReportUtil.getExcelName:133][]:income=业务总收入
[2025-07-17 13:24:03][DEBUG][http-nio-9232-exec-2][c.h.s.u.ReportUtil.getExcelName:134][]:in_out=结入
[2025-07-17 13:24:03][DEBUG][http-nio-9232-exec-2][c.h.s.u.ReportUtil.getExcelName:135][]:in_out=结入
[2025-07-17 13:24:03][DEBUG][http-nio-9232-exec-2][o.a.i.l.j.BaseJdbcLogger.debug:137][]:==>  Preparing: SELECT t.prov_nm FROM stludr.stl_province_cd t WHERE t.prov_cd= ? limit 1
[2025-07-17 13:24:03][DEBUG][http-nio-9232-exec-2][o.a.i.l.j.BaseJdbcLogger.debug:137][]:==> Parameters: 100(String)
[2025-07-17 13:24:03][DEBUG][http-nio-9232-exec-2][o.a.i.l.j.BaseJdbcLogger.debug:137][]:<==      Total: 1
[2025-07-17 13:24:03][INFO ][http-nio-9232-exec-2][c.h.s.u.ReportUtil.getExcelName:187][]:let me look look : 双跨专线结算补充报表（SRv6-VPN智能专线）-落地方分表
[2025-07-17 13:24:03][INFO ][http-nio-9232-exec-2][o.a.j.r.queryReport_jsp._jspService:194][]:从show获取到得report:other/Sk_Line_Supp_srv6-3.rpx
[2025-07-17 13:24:03][INFO ][http-nio-9232-exec-2][o.a.j.r.queryReport_jsp._jspService:201][]:报表名称：双跨专线结算补充报表（SRv6-VPN智能专线）-落地方分表，报表路径：other/Sk_Line_Supp_srv6-3.rpx，是否使用缓存:no
[2025-07-17 13:24:03][DEBUG][http-nio-9232-exec-2][o.a.i.l.j.BaseJdbcLogger.debug:137][]:==>  Preparing: SELECT decode(count(1), 0, 'N', 'Y') AS dictvalue, '' AS dictdesc FROM stludr.am_tree_check_new a WHERE a.TREE_NODE_ID = ? AND a.STATE = 'Y' AND a.ACCT_MONTH = ?
[2025-07-17 13:24:03][DEBUG][http-nio-9232-exec-2][o.a.i.l.j.BaseJdbcLogger.debug:137][]:==> Parameters: 1068(String), 202507(String)
[2025-07-17 13:24:03][DEBUG][http-nio-9232-exec-2][o.a.i.l.j.BaseJdbcLogger.debug:137][]:<==      Total: 1
[2025-07-17 13:24:03][INFO ][http-nio-9232-exec-2][c.h.s.s.i.ReportCheckServiceImpl.isCheck:69][]:******报表审核通过，本报表的编号=[1068]******
[2025-07-17 13:24:04][DEBUG][http-nio-9232-exec-2][o.a.i.l.j.BaseJdbcLogger.debug:137][]:==>  Preparing: SELECT RAQ_KEY,APPROVE_FLAG,GOLD_BANK_CODE FROM report_gold_config WHERE (APPROVE_FLAG = ? AND RAQ_KEY = ?)
[2025-07-17 13:24:04][DEBUG][http-nio-9232-exec-2][o.a.i.l.j.BaseJdbcLogger.debug:137][]:==> Parameters: Y(String), 10209(String)
[2025-07-17 13:24:04][ERROR][http-nio-9232-exec-2][o.a.j.l.DirectJDKLog.log:175][]:Servlet.service() for servlet [jsp] threw exception
java.sql.SQLSyntaxErrorException: Unknown column 'GOLD_BANK_CODE' in 'field list'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:120)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:370)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:497)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59)
	at com.sun.proxy.$Proxy142.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:64)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:79)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:63)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:89)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	at com.sun.proxy.$Proxy93.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:166)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy118.selectList(Unknown Source)
	at com.baomidou.mybatisplus.core.mapper.BaseMapper.selectOne(BaseMapper.java:173)
	at java.lang.invoke.MethodHandle.invokeWithArguments(MethodHandle.java:627)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$DefaultMethodInvoker.invoke(MybatisMapperProxy.java:162)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy118.selectOne(Unknown Source)
	at com.baomidou.mybatisplus.extension.service.impl.ServiceImpl.getOne(ServiceImpl.java:202)
	at com.baomidou.mybatisplus.extension.service.IService.getOne(IService.java:320)
	at com.hp.settle.service.impl.ReportGoldApproveImpl.isApprove(ReportGoldApproveImpl.java:49)
	at com.hp.settle.service.impl.ReportGoldApproveImpl$$FastClassBySpringCGLIB$$baea858e.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386)
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704)
	at com.hp.settle.service.impl.ReportGoldApproveImpl$$EnhancerBySpringCGLIB$$d313e785.isApprove(<generated>)
	at com.hp.settle.util.ReportUtil.isApprove(ReportUtil.java:406)
	at org.apache.jsp.reportJsp.queryReport_jsp._jspService(queryReport_jsp.java:218)
	at org.apache.jasper.runtime.HttpJspBase.service(HttpJspBase.java:70)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:750)
	at org.apache.jasper.servlet.JspServletWrapper.service(JspServletWrapper.java:466)
	at org.apache.jasper.servlet.JspServlet.serviceJspFile(JspServlet.java:379)
	at org.apache.jasper.servlet.JspServlet.service(JspServlet.java:327)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:750)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.ApplicationDispatcher.invoke(ApplicationDispatcher.java:711)
	at org.apache.catalina.core.ApplicationDispatcher.processRequest(ApplicationDispatcher.java:459)
	at org.apache.catalina.core.ApplicationDispatcher.doForward(ApplicationDispatcher.java:385)
	at org.apache.catalina.core.ApplicationDispatcher.forward(ApplicationDispatcher.java:313)
	at com.hp.settle.config.AuthenticationFilter.doFilter(AuthenticationFilter.java:160)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:177)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:891)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1784)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:745)
[2025-07-17 13:24:04][ERROR][http-nio-9232-exec-2][o.a.j.l.DirectJDKLog.log:175][]:Servlet.service() for servlet [jsp] in context with path [/settleReport] threw exception [org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column 'GOLD_BANK_CODE' in 'field list'
### The error may exist in com/hp/settle/mapper/ReportGoldApproveMapper.java (best guess)
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT  RAQ_KEY,APPROVE_FLAG,GOLD_BANK_CODE  FROM report_gold_config     WHERE (APPROVE_FLAG = ? AND RAQ_KEY = ?)
### Cause: java.sql.SQLSyntaxErrorException: Unknown column 'GOLD_BANK_CODE' in 'field list'
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'GOLD_BANK_CODE' in 'field list'] with root cause
java.sql.SQLSyntaxErrorException: Unknown column 'GOLD_BANK_CODE' in 'field list'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:120)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:370)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:497)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59)
	at com.sun.proxy.$Proxy142.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:64)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:79)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:63)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:89)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	at com.sun.proxy.$Proxy93.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:166)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy118.selectList(Unknown Source)
	at com.baomidou.mybatisplus.core.mapper.BaseMapper.selectOne(BaseMapper.java:173)
	at java.lang.invoke.MethodHandle.invokeWithArguments(MethodHandle.java:627)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$DefaultMethodInvoker.invoke(MybatisMapperProxy.java:162)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy118.selectOne(Unknown Source)
	at com.baomidou.mybatisplus.extension.service.impl.ServiceImpl.getOne(ServiceImpl.java:202)
	at com.baomidou.mybatisplus.extension.service.IService.getOne(IService.java:320)
	at com.hp.settle.service.impl.ReportGoldApproveImpl.isApprove(ReportGoldApproveImpl.java:49)
	at com.hp.settle.service.impl.ReportGoldApproveImpl$$FastClassBySpringCGLIB$$baea858e.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386)
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704)
	at com.hp.settle.service.impl.ReportGoldApproveImpl$$EnhancerBySpringCGLIB$$d313e785.isApprove(<generated>)
	at com.hp.settle.util.ReportUtil.isApprove(ReportUtil.java:406)
	at org.apache.jsp.reportJsp.queryReport_jsp._jspService(queryReport_jsp.java:218)
	at org.apache.jasper.runtime.HttpJspBase.service(HttpJspBase.java:70)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:750)
	at org.apache.jasper.servlet.JspServletWrapper.service(JspServletWrapper.java:466)
	at org.apache.jasper.servlet.JspServlet.serviceJspFile(JspServlet.java:379)
	at org.apache.jasper.servlet.JspServlet.service(JspServlet.java:327)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:750)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.ApplicationDispatcher.invoke(ApplicationDispatcher.java:711)
	at org.apache.catalina.core.ApplicationDispatcher.processRequest(ApplicationDispatcher.java:459)
	at org.apache.catalina.core.ApplicationDispatcher.doForward(ApplicationDispatcher.java:385)
	at org.apache.catalina.core.ApplicationDispatcher.forward(ApplicationDispatcher.java:313)
	at com.hp.settle.config.AuthenticationFilter.doFilter(AuthenticationFilter.java:160)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:177)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:891)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1784)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:745)
[2025-07-17 13:24:07][INFO ][http-nio-9232-exec-7][o.a.j.r.showReport_jsp._jspService:205][]:第一次获取的rKey：10209
[2025-07-17 13:24:07][DEBUG][http-nio-9232-exec-7][o.a.i.l.j.BaseJdbcLogger.debug:137][]:==>  Preparing: select * from STLUDR.RVL_REPORT_RAQ where R_KEY = ? order by R_ID desc
[2025-07-17 13:24:07][DEBUG][http-nio-9232-exec-7][o.a.i.l.j.BaseJdbcLogger.debug:137][]:==> Parameters: 10209(String)
[2025-07-17 13:24:07][DEBUG][http-nio-9232-exec-7][o.a.i.l.j.BaseJdbcLogger.debug:137][]:<==      Total: 1
[2025-07-17 13:24:07][INFO ][http-nio-9232-exec-7][c.h.s.u.ReportUtil.getShowReportJspUrl:62][]:modifiedRpx: other/Sk_Line_Supp_srv6-3.rpx
[2025-07-17 13:24:07][DEBUG][http-nio-9232-exec-7][c.h.s.u.ReportUtil.getExcelName:107][]:-----------getExcelName()
[2025-07-17 13:24:07][DEBUG][http-nio-9232-exec-7][c.h.s.u.ReportUtil.getExcelName:108][]:rKey=10209
[2025-07-17 13:24:07][DEBUG][http-nio-9232-exec-7][c.h.s.u.ReportUtil.getExcelName:113][]:ce.getRaqName=双跨专线结算补充报表（SRv6-VPN智能专线）ADJUST-落地方分表
[2025-07-17 13:24:07][DEBUG][http-nio-9232-exec-7][o.a.i.l.j.BaseJdbcLogger.debug:137][]:==>  Preparing: SELECT T.DICTDESC FROM STLUDR.STL_CONF_DICT T WHERE T.DICTVALUE = ? AND T.ITEM = 'ENTITY'
[2025-07-17 13:24:07][DEBUG][http-nio-9232-exec-7][o.a.i.l.j.BaseJdbcLogger.debug:137][]:==> Parameters: null
[2025-07-17 13:24:07][DEBUG][http-nio-9232-exec-7][o.a.i.l.j.BaseJdbcLogger.debug:137][]:<==      Total: 0
[2025-07-17 13:24:07][DEBUG][http-nio-9232-exec-7][c.h.s.u.ReportUtil.getExcelName:130][]:settlemonth=202507
[2025-07-17 13:24:07][DEBUG][http-nio-9232-exec-7][c.h.s.u.ReportUtil.getExcelName:131][]:taxrate=6
[2025-07-17 13:24:07][DEBUG][http-nio-9232-exec-7][c.h.s.u.ReportUtil.getExcelName:132][]:entity=全部
[2025-07-17 13:24:07][DEBUG][http-nio-9232-exec-7][c.h.s.u.ReportUtil.getExcelName:133][]:income=业务总收入
[2025-07-17 13:24:07][DEBUG][http-nio-9232-exec-7][c.h.s.u.ReportUtil.getExcelName:134][]:in_out=结入
[2025-07-17 13:24:07][DEBUG][http-nio-9232-exec-7][c.h.s.u.ReportUtil.getExcelName:135][]:in_out=结入
[2025-07-17 13:24:07][INFO ][http-nio-9232-exec-7][c.h.s.u.ReportUtil.getExcelName:187][]:let me look look : 双跨专线结算补充报表（SRv6-VPN智能专线）-落地方分表
[2025-07-17 13:24:07][INFO ][http-nio-9232-exec-7][o.a.j.r.showReport_jsp._jspService:209][]:report:other/Sk_Line_Supp_srv6-3.rpx
[2025-07-17 13:24:07][INFO ][http-nio-9232-exec-7][o.a.j.r.showReport_jsp._jspService:210][]:excelName:双跨专线结算补充报表（SRv6-VPN智能专线）-落地方分表
[2025-07-17 13:24:07][INFO ][http-nio-9232-exec-7][o.a.j.r.showReport_jsp._jspService:237][]:请求参数：R_KEY=10209;tree_node_id=1068;staffNum=85195550005;
[2025-07-17 13:24:07][INFO ][http-nio-9232-exec-7][o.a.j.r.showReport_jsp._jspService:241][]:other/Sk_Line_Supp_srv6-3.rpx，是否有参数：true,参数报表：other/Sk_Line_Supp_srv6-3_arg.rpx
[2025-07-17 13:24:07][DEBUG][http-nio-9232-exec-7][o.a.i.l.j.BaseJdbcLogger.debug:137][]:==>  Preparing: select * from STLUDR.RVL_REPORT_RAQ where R_KEY = ? order by R_ID desc
[2025-07-17 13:24:07][DEBUG][http-nio-9232-exec-7][o.a.i.l.j.BaseJdbcLogger.debug:137][]:==> Parameters: 10209(String)
[2025-07-17 13:24:07][DEBUG][http-nio-9232-exec-7][o.a.i.l.j.BaseJdbcLogger.debug:137][]:<==      Total: 1
[2025-07-17 13:24:07][INFO ][http-nio-9232-exec-7][c.h.s.u.ReportUtil.getShowReportJspUrl:62][]:modifiedRpx: other/Sk_Line_Supp_srv6-3.rpx
[2025-07-17 13:24:14][INFO ][http-nio-9232-exec-8][c.h.s.c.AuthenticationFilter.doFilter:101][]:进入放行路径requestURLhttp://127.0.0.1:9232/settleReport/reportServlet
[2025-07-17 13:24:16][INFO ][http-nio-9232-exec-3][c.h.s.c.AuthenticationFilter.doFilter:101][]:进入放行路径requestURLhttp://127.0.0.1:9232/settleReport/reportServlet
[2025-07-17 13:24:16][INFO ][http-nio-9232-exec-3][c.h.s.r.ReportParamPostHandle.process:19][]:paramName:settlemonth---paramValue:202507
[2025-07-17 13:24:16][INFO ][http-nio-9232-exec-1][c.h.s.c.AuthenticationFilter.doFilter:159][]:进入 /reportJsp/queryReport.jsp
[2025-07-17 13:24:16][INFO ][http-nio-9232-exec-1][o.a.j.r.queryReport_jsp._jspService:169][]:context-path:/settleReport
[2025-07-17 13:24:16][INFO ][http-nio-9232-exec-1][o.a.j.r.queryReport_jsp._jspService:171][]:第3次获取的rKey：10209
[2025-07-17 13:24:16][INFO ][http-nio-9232-exec-1][o.a.j.r.queryReport_jsp._jspService:173][]:第4次获取的rKey1：10209
[2025-07-17 13:24:16][INFO ][http-nio-9232-exec-1][o.a.j.r.queryReport_jsp._jspService:179][]:联动报表4进来了
[2025-07-17 13:24:16][INFO ][http-nio-9232-exec-1][o.a.j.r.queryReport_jsp._jspService:181][]:联动报表4进来了获取的rKey：10209
[2025-07-17 13:24:16][INFO ][http-nio-9232-exec-1][o.a.j.r.queryReport_jsp._jspService:187][]:settlemonth:202507
[2025-07-17 13:24:16][DEBUG][http-nio-9232-exec-1][o.a.i.l.j.BaseJdbcLogger.debug:137][]:==>  Preparing: select * from STLUDR.RVL_REPORT_RAQ where R_KEY = ? and ? between START_PAYMENT and END_PAYMENT order by R_ID desc limit 1
[2025-07-17 13:24:16][DEBUG][http-nio-9232-exec-1][o.a.i.l.j.BaseJdbcLogger.debug:137][]:==> Parameters: 10209(String), 202507(String)
[2025-07-17 13:24:16][DEBUG][http-nio-9232-exec-1][o.a.i.l.j.BaseJdbcLogger.debug:137][]:<==      Total: 1
[2025-07-17 13:24:16][DEBUG][http-nio-9232-exec-1][c.h.s.u.ReportUtil.getExcelName:107][]:-----------getExcelName()
[2025-07-17 13:24:16][DEBUG][http-nio-9232-exec-1][c.h.s.u.ReportUtil.getExcelName:108][]:rKey=10209
[2025-07-17 13:24:16][DEBUG][http-nio-9232-exec-1][c.h.s.u.ReportUtil.getExcelName:113][]:ce.getRaqName=双跨专线结算补充报表（SRv6-VPN智能专线）ADJUST-落地方分表
[2025-07-17 13:24:16][DEBUG][http-nio-9232-exec-1][o.a.i.l.j.BaseJdbcLogger.debug:137][]:==>  Preparing: SELECT T.DICTDESC FROM STLUDR.STL_CONF_DICT T WHERE T.DICTVALUE = ? AND T.ITEM = 'ENTITY'
[2025-07-17 13:24:16][DEBUG][http-nio-9232-exec-1][o.a.i.l.j.BaseJdbcLogger.debug:137][]:==> Parameters: null
[2025-07-17 13:24:16][DEBUG][http-nio-9232-exec-1][o.a.i.l.j.BaseJdbcLogger.debug:137][]:<==      Total: 0
[2025-07-17 13:24:16][DEBUG][http-nio-9232-exec-1][c.h.s.u.ReportUtil.getExcelName:130][]:settlemonth=202507
[2025-07-17 13:24:16][DEBUG][http-nio-9232-exec-1][c.h.s.u.ReportUtil.getExcelName:131][]:taxrate=6
[2025-07-17 13:24:16][DEBUG][http-nio-9232-exec-1][c.h.s.u.ReportUtil.getExcelName:132][]:entity=全部
[2025-07-17 13:24:16][DEBUG][http-nio-9232-exec-1][c.h.s.u.ReportUtil.getExcelName:133][]:income=业务总收入
[2025-07-17 13:24:16][DEBUG][http-nio-9232-exec-1][c.h.s.u.ReportUtil.getExcelName:134][]:in_out=结入
[2025-07-17 13:24:16][DEBUG][http-nio-9232-exec-1][c.h.s.u.ReportUtil.getExcelName:135][]:in_out=结入
[2025-07-17 13:24:16][DEBUG][http-nio-9232-exec-1][o.a.i.l.j.BaseJdbcLogger.debug:137][]:==>  Preparing: SELECT t.prov_nm FROM stludr.stl_province_cd t WHERE t.prov_cd= ? limit 1
[2025-07-17 13:24:16][DEBUG][http-nio-9232-exec-1][o.a.i.l.j.BaseJdbcLogger.debug:137][]:==> Parameters: 100(String)
[2025-07-17 13:24:16][DEBUG][http-nio-9232-exec-1][o.a.i.l.j.BaseJdbcLogger.debug:137][]:<==      Total: 1
[2025-07-17 13:24:16][INFO ][http-nio-9232-exec-1][c.h.s.u.ReportUtil.getExcelName:187][]:let me look look : 双跨专线结算补充报表（SRv6-VPN智能专线）-落地方分表
[2025-07-17 13:24:16][INFO ][http-nio-9232-exec-1][o.a.j.r.queryReport_jsp._jspService:194][]:从show获取到得report:other/Sk_Line_Supp_srv6-3.rpx
[2025-07-17 13:24:16][INFO ][http-nio-9232-exec-1][o.a.j.r.queryReport_jsp._jspService:201][]:报表名称：双跨专线结算补充报表（SRv6-VPN智能专线）-落地方分表，报表路径：other/Sk_Line_Supp_srv6-3.rpx，是否使用缓存:no
[2025-07-17 13:24:16][DEBUG][http-nio-9232-exec-1][o.a.i.l.j.BaseJdbcLogger.debug:137][]:==>  Preparing: SELECT decode(count(1), 0, 'N', 'Y') AS dictvalue, '' AS dictdesc FROM stludr.am_tree_check_new a WHERE a.TREE_NODE_ID = ? AND a.STATE = 'Y' AND a.ACCT_MONTH = ?
[2025-07-17 13:24:16][DEBUG][http-nio-9232-exec-1][o.a.i.l.j.BaseJdbcLogger.debug:137][]:==> Parameters: 1068(String), 202507(String)
[2025-07-17 13:24:16][DEBUG][http-nio-9232-exec-1][o.a.i.l.j.BaseJdbcLogger.debug:137][]:<==      Total: 1
[2025-07-17 13:24:16][INFO ][http-nio-9232-exec-1][c.h.s.s.i.ReportCheckServiceImpl.isCheck:69][]:******报表审核通过，本报表的编号=[1068]******
[2025-07-17 13:24:17][DEBUG][http-nio-9232-exec-1][o.a.i.l.j.BaseJdbcLogger.debug:137][]:==>  Preparing: SELECT RAQ_KEY,APPROVE_FLAG,GOLD_BANK_CODE FROM report_gold_config WHERE (APPROVE_FLAG = ? AND RAQ_KEY = ?)
[2025-07-17 13:24:17][DEBUG][http-nio-9232-exec-1][o.a.i.l.j.BaseJdbcLogger.debug:137][]:==> Parameters: Y(String), 10209(String)
[2025-07-17 13:24:17][ERROR][http-nio-9232-exec-1][o.a.j.l.DirectJDKLog.log:175][]:Servlet.service() for servlet [jsp] threw exception
java.sql.SQLSyntaxErrorException: Unknown column 'GOLD_BANK_CODE' in 'field list'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:120)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:370)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:497)
	at sun.reflect.GeneratedMethodAccessor44.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59)
	at com.sun.proxy.$Proxy142.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:64)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:79)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:63)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:89)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	at com.sun.proxy.$Proxy93.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:166)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy118.selectList(Unknown Source)
	at com.baomidou.mybatisplus.core.mapper.BaseMapper.selectOne(BaseMapper.java:173)
	at java.lang.invoke.MethodHandle.invokeWithArguments(MethodHandle.java:627)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$DefaultMethodInvoker.invoke(MybatisMapperProxy.java:162)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy118.selectOne(Unknown Source)
	at com.baomidou.mybatisplus.extension.service.impl.ServiceImpl.getOne(ServiceImpl.java:202)
	at com.baomidou.mybatisplus.extension.service.IService.getOne(IService.java:320)
	at com.hp.settle.service.impl.ReportGoldApproveImpl.isApprove(ReportGoldApproveImpl.java:49)
	at com.hp.settle.service.impl.ReportGoldApproveImpl$$FastClassBySpringCGLIB$$baea858e.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386)
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704)
	at com.hp.settle.service.impl.ReportGoldApproveImpl$$EnhancerBySpringCGLIB$$d313e785.isApprove(<generated>)
	at com.hp.settle.util.ReportUtil.isApprove(ReportUtil.java:406)
	at org.apache.jsp.reportJsp.queryReport_jsp._jspService(queryReport_jsp.java:218)
	at org.apache.jasper.runtime.HttpJspBase.service(HttpJspBase.java:70)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:750)
	at org.apache.jasper.servlet.JspServletWrapper.service(JspServletWrapper.java:466)
	at org.apache.jasper.servlet.JspServlet.serviceJspFile(JspServlet.java:379)
	at org.apache.jasper.servlet.JspServlet.service(JspServlet.java:327)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:750)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.ApplicationDispatcher.invoke(ApplicationDispatcher.java:711)
	at org.apache.catalina.core.ApplicationDispatcher.processRequest(ApplicationDispatcher.java:459)
	at org.apache.catalina.core.ApplicationDispatcher.doForward(ApplicationDispatcher.java:385)
	at org.apache.catalina.core.ApplicationDispatcher.forward(ApplicationDispatcher.java:313)
	at com.hp.settle.config.AuthenticationFilter.doFilter(AuthenticationFilter.java:160)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:177)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:891)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1784)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:745)
[2025-07-17 13:24:17][ERROR][http-nio-9232-exec-1][o.a.j.l.DirectJDKLog.log:175][]:Servlet.service() for servlet [jsp] in context with path [/settleReport] threw exception [org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column 'GOLD_BANK_CODE' in 'field list'
### The error may exist in com/hp/settle/mapper/ReportGoldApproveMapper.java (best guess)
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT  RAQ_KEY,APPROVE_FLAG,GOLD_BANK_CODE  FROM report_gold_config     WHERE (APPROVE_FLAG = ? AND RAQ_KEY = ?)
### Cause: java.sql.SQLSyntaxErrorException: Unknown column 'GOLD_BANK_CODE' in 'field list'
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'GOLD_BANK_CODE' in 'field list'] with root cause
java.sql.SQLSyntaxErrorException: Unknown column 'GOLD_BANK_CODE' in 'field list'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:120)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:370)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:497)
	at sun.reflect.GeneratedMethodAccessor44.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59)
	at com.sun.proxy.$Proxy142.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:64)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:79)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:63)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:89)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	at com.sun.proxy.$Proxy93.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:166)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy118.selectList(Unknown Source)
	at com.baomidou.mybatisplus.core.mapper.BaseMapper.selectOne(BaseMapper.java:173)
	at java.lang.invoke.MethodHandle.invokeWithArguments(MethodHandle.java:627)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$DefaultMethodInvoker.invoke(MybatisMapperProxy.java:162)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy118.selectOne(Unknown Source)
	at com.baomidou.mybatisplus.extension.service.impl.ServiceImpl.getOne(ServiceImpl.java:202)
	at com.baomidou.mybatisplus.extension.service.IService.getOne(IService.java:320)
	at com.hp.settle.service.impl.ReportGoldApproveImpl.isApprove(ReportGoldApproveImpl.java:49)
	at com.hp.settle.service.impl.ReportGoldApproveImpl$$FastClassBySpringCGLIB$$baea858e.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386)
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704)
	at com.hp.settle.service.impl.ReportGoldApproveImpl$$EnhancerBySpringCGLIB$$d313e785.isApprove(<generated>)
	at com.hp.settle.util.ReportUtil.isApprove(ReportUtil.java:406)
	at org.apache.jsp.reportJsp.queryReport_jsp._jspService(queryReport_jsp.java:218)
	at org.apache.jasper.runtime.HttpJspBase.service(HttpJspBase.java:70)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:750)
	at org.apache.jasper.servlet.JspServletWrapper.service(JspServletWrapper.java:466)
	at org.apache.jasper.servlet.JspServlet.serviceJspFile(JspServlet.java:379)
	at org.apache.jasper.servlet.JspServlet.service(JspServlet.java:327)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:750)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.ApplicationDispatcher.invoke(ApplicationDispatcher.java:711)
	at org.apache.catalina.core.ApplicationDispatcher.processRequest(ApplicationDispatcher.java:459)
	at org.apache.catalina.core.ApplicationDispatcher.doForward(ApplicationDispatcher.java:385)
	at org.apache.catalina.core.ApplicationDispatcher.forward(ApplicationDispatcher.java:313)
	at com.hp.settle.config.AuthenticationFilter.doFilter(AuthenticationFilter.java:160)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:177)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:891)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1784)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:745)
[2025-07-17 13:24:35][INFO ][http-nio-9232-exec-10][o.a.j.r.showReport_jsp._jspService:205][]:第一次获取的rKey：10209
[2025-07-17 13:24:35][DEBUG][http-nio-9232-exec-10][o.a.i.l.j.BaseJdbcLogger.debug:137][]:==>  Preparing: select * from STLUDR.RVL_REPORT_RAQ where R_KEY = ? order by R_ID desc
[2025-07-17 13:24:35][DEBUG][http-nio-9232-exec-10][o.a.i.l.j.BaseJdbcLogger.debug:137][]:==> Parameters: 10209(String)
[2025-07-17 13:24:35][DEBUG][http-nio-9232-exec-10][o.a.i.l.j.BaseJdbcLogger.debug:137][]:<==      Total: 1
[2025-07-17 13:24:35][INFO ][http-nio-9232-exec-10][c.h.s.u.ReportUtil.getShowReportJspUrl:62][]:modifiedRpx: other/Sk_Line_Supp_srv6-3.rpx
[2025-07-17 13:24:35][DEBUG][http-nio-9232-exec-10][c.h.s.u.ReportUtil.getExcelName:107][]:-----------getExcelName()
[2025-07-17 13:24:35][DEBUG][http-nio-9232-exec-10][c.h.s.u.ReportUtil.getExcelName:108][]:rKey=10209
[2025-07-17 13:24:35][DEBUG][http-nio-9232-exec-10][c.h.s.u.ReportUtil.getExcelName:113][]:ce.getRaqName=双跨专线结算补充报表（SRv6-VPN智能专线）ADJUST-落地方分表
[2025-07-17 13:24:35][DEBUG][http-nio-9232-exec-10][o.a.i.l.j.BaseJdbcLogger.debug:137][]:==>  Preparing: SELECT T.DICTDESC FROM STLUDR.STL_CONF_DICT T WHERE T.DICTVALUE = ? AND T.ITEM = 'ENTITY'
[2025-07-17 13:24:35][DEBUG][http-nio-9232-exec-10][o.a.i.l.j.BaseJdbcLogger.debug:137][]:==> Parameters: null
[2025-07-17 13:24:35][DEBUG][http-nio-9232-exec-10][o.a.i.l.j.BaseJdbcLogger.debug:137][]:<==      Total: 0
[2025-07-17 13:24:35][DEBUG][http-nio-9232-exec-10][c.h.s.u.ReportUtil.getExcelName:130][]:settlemonth=202507
[2025-07-17 13:24:35][DEBUG][http-nio-9232-exec-10][c.h.s.u.ReportUtil.getExcelName:131][]:taxrate=6
[2025-07-17 13:24:35][DEBUG][http-nio-9232-exec-10][c.h.s.u.ReportUtil.getExcelName:132][]:entity=全部
[2025-07-17 13:24:35][DEBUG][http-nio-9232-exec-10][c.h.s.u.ReportUtil.getExcelName:133][]:income=业务总收入
[2025-07-17 13:24:35][DEBUG][http-nio-9232-exec-10][c.h.s.u.ReportUtil.getExcelName:134][]:in_out=结入
[2025-07-17 13:24:35][DEBUG][http-nio-9232-exec-10][c.h.s.u.ReportUtil.getExcelName:135][]:in_out=结入
[2025-07-17 13:24:35][INFO ][http-nio-9232-exec-10][c.h.s.u.ReportUtil.getExcelName:187][]:let me look look : 双跨专线结算补充报表（SRv6-VPN智能专线）-落地方分表
[2025-07-17 13:24:35][INFO ][http-nio-9232-exec-10][o.a.j.r.showReport_jsp._jspService:209][]:report:other/Sk_Line_Supp_srv6-3.rpx
[2025-07-17 13:24:35][INFO ][http-nio-9232-exec-10][o.a.j.r.showReport_jsp._jspService:210][]:excelName:双跨专线结算补充报表（SRv6-VPN智能专线）-落地方分表
[2025-07-17 13:24:35][INFO ][http-nio-9232-exec-10][o.a.j.r.showReport_jsp._jspService:237][]:请求参数：R_KEY=10209;tree_node_id=1068;staffNum=85195550005;
[2025-07-17 13:24:35][INFO ][http-nio-9232-exec-10][o.a.j.r.showReport_jsp._jspService:241][]:other/Sk_Line_Supp_srv6-3.rpx，是否有参数：true,参数报表：other/Sk_Line_Supp_srv6-3_arg.rpx
[2025-07-17 13:24:35][DEBUG][http-nio-9232-exec-10][o.a.i.l.j.BaseJdbcLogger.debug:137][]:==>  Preparing: select * from STLUDR.RVL_REPORT_RAQ where R_KEY = ? order by R_ID desc
[2025-07-17 13:24:35][DEBUG][http-nio-9232-exec-10][o.a.i.l.j.BaseJdbcLogger.debug:137][]:==> Parameters: 10209(String)
[2025-07-17 13:24:36][DEBUG][http-nio-9232-exec-10][o.a.i.l.j.BaseJdbcLogger.debug:137][]:<==      Total: 1
[2025-07-17 13:24:36][INFO ][http-nio-9232-exec-10][c.h.s.u.ReportUtil.getShowReportJspUrl:62][]:modifiedRpx: other/Sk_Line_Supp_srv6-3.rpx
[2025-07-17 13:24:42][INFO ][http-nio-9232-exec-5][c.h.s.c.AuthenticationFilter.doFilter:101][]:进入放行路径requestURLhttp://127.0.0.1:9232/settleReport/reportServlet
[2025-07-17 13:24:45][INFO ][http-nio-9232-exec-9][c.h.s.c.AuthenticationFilter.doFilter:101][]:进入放行路径requestURLhttp://127.0.0.1:9232/settleReport/reportServlet
[2025-07-17 13:24:45][INFO ][http-nio-9232-exec-9][c.h.s.r.ReportParamPostHandle.process:19][]:paramName:settlemonth---paramValue:202507
[2025-07-17 13:24:45][INFO ][http-nio-9232-exec-4][c.h.s.c.AuthenticationFilter.doFilter:159][]:进入 /reportJsp/queryReport.jsp
[2025-07-17 13:24:45][INFO ][http-nio-9232-exec-4][o.a.j.r.queryReport_jsp._jspService:169][]:context-path:/settleReport
[2025-07-17 13:24:45][INFO ][http-nio-9232-exec-4][o.a.j.r.queryReport_jsp._jspService:171][]:第3次获取的rKey：10209
[2025-07-17 13:24:45][INFO ][http-nio-9232-exec-4][o.a.j.r.queryReport_jsp._jspService:173][]:第4次获取的rKey1：10209
[2025-07-17 13:24:45][INFO ][http-nio-9232-exec-4][o.a.j.r.queryReport_jsp._jspService:179][]:联动报表4进来了
[2025-07-17 13:24:45][INFO ][http-nio-9232-exec-4][o.a.j.r.queryReport_jsp._jspService:181][]:联动报表4进来了获取的rKey：10209
[2025-07-17 13:24:45][INFO ][http-nio-9232-exec-4][o.a.j.r.queryReport_jsp._jspService:187][]:settlemonth:202507
[2025-07-17 13:24:45][DEBUG][http-nio-9232-exec-4][o.a.i.l.j.BaseJdbcLogger.debug:137][]:==>  Preparing: select * from STLUDR.RVL_REPORT_RAQ where R_KEY = ? and ? between START_PAYMENT and END_PAYMENT order by R_ID desc limit 1
[2025-07-17 13:24:45][DEBUG][http-nio-9232-exec-4][o.a.i.l.j.BaseJdbcLogger.debug:137][]:==> Parameters: 10209(String), 202507(String)
[2025-07-17 13:24:45][DEBUG][http-nio-9232-exec-4][o.a.i.l.j.BaseJdbcLogger.debug:137][]:<==      Total: 1
[2025-07-17 13:24:45][DEBUG][http-nio-9232-exec-4][c.h.s.u.ReportUtil.getExcelName:107][]:-----------getExcelName()
[2025-07-17 13:24:45][DEBUG][http-nio-9232-exec-4][c.h.s.u.ReportUtil.getExcelName:108][]:rKey=10209
[2025-07-17 13:24:45][DEBUG][http-nio-9232-exec-4][c.h.s.u.ReportUtil.getExcelName:113][]:ce.getRaqName=双跨专线结算补充报表（SRv6-VPN智能专线）ADJUST-落地方分表
[2025-07-17 13:24:45][DEBUG][http-nio-9232-exec-4][o.a.i.l.j.BaseJdbcLogger.debug:137][]:==>  Preparing: SELECT T.DICTDESC FROM STLUDR.STL_CONF_DICT T WHERE T.DICTVALUE = ? AND T.ITEM = 'ENTITY'
[2025-07-17 13:24:45][DEBUG][http-nio-9232-exec-4][o.a.i.l.j.BaseJdbcLogger.debug:137][]:==> Parameters: null
[2025-07-17 13:24:45][DEBUG][http-nio-9232-exec-4][o.a.i.l.j.BaseJdbcLogger.debug:137][]:<==      Total: 0
[2025-07-17 13:24:45][DEBUG][http-nio-9232-exec-4][c.h.s.u.ReportUtil.getExcelName:130][]:settlemonth=202507
[2025-07-17 13:24:45][DEBUG][http-nio-9232-exec-4][c.h.s.u.ReportUtil.getExcelName:131][]:taxrate=6
[2025-07-17 13:24:45][DEBUG][http-nio-9232-exec-4][c.h.s.u.ReportUtil.getExcelName:132][]:entity=全部
[2025-07-17 13:24:45][DEBUG][http-nio-9232-exec-4][c.h.s.u.ReportUtil.getExcelName:133][]:income=业务总收入
[2025-07-17 13:24:45][DEBUG][http-nio-9232-exec-4][c.h.s.u.ReportUtil.getExcelName:134][]:in_out=结入
[2025-07-17 13:24:45][DEBUG][http-nio-9232-exec-4][c.h.s.u.ReportUtil.getExcelName:135][]:in_out=结入
[2025-07-17 13:24:45][DEBUG][http-nio-9232-exec-4][o.a.i.l.j.BaseJdbcLogger.debug:137][]:==>  Preparing: SELECT t.prov_nm FROM stludr.stl_province_cd t WHERE t.prov_cd= ? limit 1
[2025-07-17 13:24:45][DEBUG][http-nio-9232-exec-4][o.a.i.l.j.BaseJdbcLogger.debug:137][]:==> Parameters: 100(String)
[2025-07-17 13:24:45][DEBUG][http-nio-9232-exec-4][o.a.i.l.j.BaseJdbcLogger.debug:137][]:<==      Total: 1
[2025-07-17 13:24:45][INFO ][http-nio-9232-exec-4][c.h.s.u.ReportUtil.getExcelName:187][]:let me look look : 双跨专线结算补充报表（SRv6-VPN智能专线）-落地方分表
[2025-07-17 13:24:45][INFO ][http-nio-9232-exec-4][o.a.j.r.queryReport_jsp._jspService:194][]:从show获取到得report:other/Sk_Line_Supp_srv6-3.rpx
[2025-07-17 13:24:45][INFO ][http-nio-9232-exec-4][o.a.j.r.queryReport_jsp._jspService:201][]:报表名称：双跨专线结算补充报表（SRv6-VPN智能专线）-落地方分表，报表路径：other/Sk_Line_Supp_srv6-3.rpx，是否使用缓存:no
[2025-07-17 13:24:45][DEBUG][http-nio-9232-exec-4][o.a.i.l.j.BaseJdbcLogger.debug:137][]:==>  Preparing: SELECT decode(count(1), 0, 'N', 'Y') AS dictvalue, '' AS dictdesc FROM stludr.am_tree_check_new a WHERE a.TREE_NODE_ID = ? AND a.STATE = 'Y' AND a.ACCT_MONTH = ?
[2025-07-17 13:24:45][DEBUG][http-nio-9232-exec-4][o.a.i.l.j.BaseJdbcLogger.debug:137][]:==> Parameters: 1068(String), 202507(String)
[2025-07-17 13:24:45][DEBUG][http-nio-9232-exec-4][o.a.i.l.j.BaseJdbcLogger.debug:137][]:<==      Total: 1
[2025-07-17 13:24:45][INFO ][http-nio-9232-exec-4][c.h.s.s.i.ReportCheckServiceImpl.isCheck:69][]:******报表审核通过，本报表的编号=[1068]******
[2025-07-17 13:24:49][DEBUG][http-nio-9232-exec-4][o.a.i.l.j.BaseJdbcLogger.debug:137][]:==>  Preparing: SELECT RAQ_KEY,APPROVE_FLAG,GOLD_BANK_CODE FROM report_gold_config WHERE (APPROVE_FLAG = ? AND RAQ_KEY = ?)
[2025-07-17 13:24:49][DEBUG][http-nio-9232-exec-4][o.a.i.l.j.BaseJdbcLogger.debug:137][]:==> Parameters: Y(String), 10209(String)
[2025-07-17 13:25:04][ERROR][http-nio-9232-exec-4][o.a.j.l.DirectJDKLog.log:175][]:Servlet.service() for servlet [jsp] threw exception
java.sql.SQLSyntaxErrorException: Unknown column 'GOLD_BANK_CODE' in 'field list'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:120)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:370)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:497)
	at sun.reflect.GeneratedMethodAccessor44.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59)
	at com.sun.proxy.$Proxy142.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:64)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:79)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:63)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:89)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	at com.sun.proxy.$Proxy93.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:166)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy118.selectList(Unknown Source)
	at com.baomidou.mybatisplus.core.mapper.BaseMapper.selectOne(BaseMapper.java:173)
	at java.lang.invoke.MethodHandle.invokeWithArguments(MethodHandle.java:627)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$DefaultMethodInvoker.invoke(MybatisMapperProxy.java:162)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy118.selectOne(Unknown Source)
	at com.baomidou.mybatisplus.extension.service.impl.ServiceImpl.getOne(ServiceImpl.java:202)
	at com.baomidou.mybatisplus.extension.service.IService.getOne(IService.java:320)
	at com.hp.settle.service.impl.ReportGoldApproveImpl.isApprove(ReportGoldApproveImpl.java:49)
	at com.hp.settle.service.impl.ReportGoldApproveImpl$$FastClassBySpringCGLIB$$baea858e.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386)
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704)
	at com.hp.settle.service.impl.ReportGoldApproveImpl$$EnhancerBySpringCGLIB$$d313e785.isApprove(<generated>)
	at com.hp.settle.util.ReportUtil.isApprove(ReportUtil.java:406)
	at org.apache.jsp.reportJsp.queryReport_jsp._jspService(queryReport_jsp.java:218)
	at org.apache.jasper.runtime.HttpJspBase.service(HttpJspBase.java:70)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:750)
	at org.apache.jasper.servlet.JspServletWrapper.service(JspServletWrapper.java:466)
	at org.apache.jasper.servlet.JspServlet.serviceJspFile(JspServlet.java:379)
	at org.apache.jasper.servlet.JspServlet.service(JspServlet.java:327)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:750)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.ApplicationDispatcher.invoke(ApplicationDispatcher.java:711)
	at org.apache.catalina.core.ApplicationDispatcher.processRequest(ApplicationDispatcher.java:459)
	at org.apache.catalina.core.ApplicationDispatcher.doForward(ApplicationDispatcher.java:385)
	at org.apache.catalina.core.ApplicationDispatcher.forward(ApplicationDispatcher.java:313)
	at com.hp.settle.config.AuthenticationFilter.doFilter(AuthenticationFilter.java:160)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:177)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:891)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1784)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:745)
[2025-07-17 13:25:04][ERROR][http-nio-9232-exec-4][o.a.j.l.DirectJDKLog.log:175][]:Servlet.service() for servlet [jsp] in context with path [/settleReport] threw exception [org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column 'GOLD_BANK_CODE' in 'field list'
### The error may exist in com/hp/settle/mapper/ReportGoldApproveMapper.java (best guess)
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT  RAQ_KEY,APPROVE_FLAG,GOLD_BANK_CODE  FROM report_gold_config     WHERE (APPROVE_FLAG = ? AND RAQ_KEY = ?)
### Cause: java.sql.SQLSyntaxErrorException: Unknown column 'GOLD_BANK_CODE' in 'field list'
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'GOLD_BANK_CODE' in 'field list'] with root cause
java.sql.SQLSyntaxErrorException: Unknown column 'GOLD_BANK_CODE' in 'field list'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:120)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:370)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:497)
	at sun.reflect.GeneratedMethodAccessor44.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59)
	at com.sun.proxy.$Proxy142.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:64)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:79)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:63)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:89)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	at com.sun.proxy.$Proxy93.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:166)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy118.selectList(Unknown Source)
	at com.baomidou.mybatisplus.core.mapper.BaseMapper.selectOne(BaseMapper.java:173)
	at java.lang.invoke.MethodHandle.invokeWithArguments(MethodHandle.java:627)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$DefaultMethodInvoker.invoke(MybatisMapperProxy.java:162)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy118.selectOne(Unknown Source)
	at com.baomidou.mybatisplus.extension.service.impl.ServiceImpl.getOne(ServiceImpl.java:202)
	at com.baomidou.mybatisplus.extension.service.IService.getOne(IService.java:320)
	at com.hp.settle.service.impl.ReportGoldApproveImpl.isApprove(ReportGoldApproveImpl.java:49)
	at com.hp.settle.service.impl.ReportGoldApproveImpl$$FastClassBySpringCGLIB$$baea858e.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386)
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704)
	at com.hp.settle.service.impl.ReportGoldApproveImpl$$EnhancerBySpringCGLIB$$d313e785.isApprove(<generated>)
	at com.hp.settle.util.ReportUtil.isApprove(ReportUtil.java:406)
	at org.apache.jsp.reportJsp.queryReport_jsp._jspService(queryReport_jsp.java:218)
	at org.apache.jasper.runtime.HttpJspBase.service(HttpJspBase.java:70)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:750)
	at org.apache.jasper.servlet.JspServletWrapper.service(JspServletWrapper.java:466)
	at org.apache.jasper.servlet.JspServlet.serviceJspFile(JspServlet.java:379)
	at org.apache.jasper.servlet.JspServlet.service(JspServlet.java:327)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:750)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.ApplicationDispatcher.invoke(ApplicationDispatcher.java:711)
	at org.apache.catalina.core.ApplicationDispatcher.processRequest(ApplicationDispatcher.java:459)
	at org.apache.catalina.core.ApplicationDispatcher.doForward(ApplicationDispatcher.java:385)
	at org.apache.catalina.core.ApplicationDispatcher.forward(ApplicationDispatcher.java:313)
	at com.hp.settle.config.AuthenticationFilter.doFilter(AuthenticationFilter.java:160)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:177)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:891)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1784)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:745)
[2025-07-17 13:26:45][INFO ][main][o.s.b.SpringApplication.logStartupProfileInfo:637][]:The following 1 profile is active: "dev"
[2025-07-17 13:26:46][INFO ][main][o.s.d.r.c.RepositoryConfigurationDelegate.multipleStoresDetected:262][]:Multiple Spring Data modules found, entering strict repository configuration mode
[2025-07-17 13:26:46][INFO ][main][o.s.d.r.c.RepositoryConfigurationDelegate.registerRepositoriesIn:132][]:Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[2025-07-17 13:26:46][INFO ][main][o.s.d.r.c.RepositoryConfigurationDelegate.registerRepositoriesIn:201][]:Finished Spring Data repository scanning in 19 ms. Found 0 Redis repository interfaces.
[2025-07-17 13:26:46][INFO ][main][o.s.c.c.s.GenericScope.setSerializationId:283][]:BeanFactory id=dbe784e5-818f-3f46-bd86-89a8e46f5497
[2025-07-17 13:26:46][INFO ][main][c.u.j.c.EnableEncryptablePropertiesBeanFactoryPostProcessor.postProcessBeanFactory:39][]:Post-processing PropertySource instances
[2025-07-17 13:26:46][INFO ][main][c.u.j.EncryptablePropertySourceConverter.makeEncryptable:56][]:Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[2025-07-17 13:26:46][INFO ][main][c.u.j.EncryptablePropertySourceConverter.makeEncryptable:56][]:Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[2025-07-17 13:26:46][INFO ][main][c.u.j.EncryptablePropertySourceConverter.makeEncryptable:56][]:Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[2025-07-17 13:26:46][INFO ][main][c.u.j.EncryptablePropertySourceConverter.makeEncryptable:56][]:Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[2025-07-17 13:26:46][INFO ][main][c.u.j.EncryptablePropertySourceConverter.makeEncryptable:56][]:Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[2025-07-17 13:26:46][INFO ][main][c.u.j.EncryptablePropertySourceConverter.makeEncryptable:56][]:Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[2025-07-17 13:26:46][INFO ][main][c.u.j.EncryptablePropertySourceConverter.makeEncryptable:56][]:Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[2025-07-17 13:26:46][INFO ][main][c.u.j.EncryptablePropertySourceConverter.makeEncryptable:56][]:Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[2025-07-17 13:26:46][INFO ][main][c.u.j.EncryptablePropertySourceConverter.makeEncryptable:56][]:Converting PropertySource Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[2025-07-17 13:26:46][INFO ][main][c.u.j.EncryptablePropertySourceConverter.makeEncryptable:56][]:Converting PropertySource Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[2025-07-17 13:26:46][INFO ][main][c.u.j.EncryptablePropertySourceConverter.makeEncryptable:56][]:Converting PropertySource Config resource 'class path resource [application.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[2025-07-17 13:26:46][INFO ][main][c.u.j.EncryptablePropertySourceConverter.makeEncryptable:56][]:Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[2025-07-17 13:26:46][INFO ][main][c.u.j.EncryptablePropertySourceConverter.makeEncryptable:56][]:Converting PropertySource defaultProperties [org.springframework.boot.DefaultPropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[2025-07-17 13:26:46][INFO ][main][c.u.j.f.DefaultLazyPropertyFilter.lambda$null$2:31][]:Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[2025-07-17 13:26:46][INFO ][main][c.u.j.r.DefaultLazyPropertyResolver.lambda$null$2:35][]:Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[2025-07-17 13:26:46][INFO ][main][c.u.j.d.DefaultLazyPropertyDetector.lambda$null$2:35][]:Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[2025-07-17 13:26:47][DEBUG][main][o.s.b.w.s.s.DocumentRoot.getArchiveFileDocumentRoot:81][]:Code archive: D:\04server\apache-maven-3.6.2\repository3\org\springframework\boot\spring-boot\2.7.6\spring-boot-2.7.6.jar
[2025-07-17 13:26:47][DEBUG][main][o.s.b.w.s.s.DocumentRoot.getExplodedWarFileDocumentRoot:125][]:Code archive: D:\04server\apache-maven-3.6.2\repository3\org\springframework\boot\spring-boot\2.7.6\spring-boot-2.7.6.jar
[2025-07-17 13:26:47][DEBUG][main][o.s.b.w.s.s.DocumentRoot.getValidDirectory:69][]:Document root: D:\05code\newsettle\settle-service-report\src\main\webapp
[2025-07-17 13:26:47][INFO ][main][o.s.b.w.e.t.TomcatWebServer.initialize:108][]:Tomcat initialized with port(s): 9232 (http)
[2025-07-17 13:26:47][INFO ][main][o.a.j.l.DirectJDKLog.log:173][]:Initializing ProtocolHandler ["http-nio-9232"]
[2025-07-17 13:26:47][INFO ][main][o.a.j.l.DirectJDKLog.log:173][]:Starting service [Tomcat]
[2025-07-17 13:26:47][INFO ][main][o.a.j.l.DirectJDKLog.log:173][]:Starting Servlet engine: [Apache Tomcat/9.0.69]
[2025-07-17 13:26:47][INFO ][main][o.a.j.l.DirectJDKLog.log:173][]:At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
[2025-07-17 13:26:47][INFO ][main][o.a.j.l.DirectJDKLog.log:173][]:Initializing Spring embedded WebApplicationContext
[2025-07-17 13:26:47][INFO ][main][o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:292][]:Root WebApplicationContext: initialization completed in 1986 ms
[2025-07-17 13:26:47][INFO ][main][c.h.s.c.ReportWebConfig.servletRegistrationBean:30][]:NACOS_SERVER:null
[2025-07-17 13:26:47][INFO ][main][c.h.s.c.ReportWebConfig.servletRegistrationBean:38][]:开发环境部署-配置文件：raqsoftConfig.xml
[2025-07-17 13:26:47][INFO ][main][c.a.d.s.b.a.DruidDataSourceAutoConfigure.dataSource:56][]:Init DruidDataSource
[2025-07-17 13:26:47][INFO ][main][c.a.d.p.DruidDataSource.init:985][]:{dataSource-1} inited
[2025-07-17 13:26:49][INFO ][main][c.h.c.b.s.c.r.RedisPoolConfiguration.redisPoolConfig:279][]:redis 连接池配置
[2025-07-17 13:26:49][INFO ][main][c.h.c.b.s.c.r.RedisPoolConfiguration.redisConnectionFactory:94][]:redis factory 加载
[2025-07-17 13:26:49][INFO ][main][c.h.c.b.s.c.r.RedisPoolConfiguration.getRedisStandaloneConfiguration:230][]:redis 单机配置
[2025-07-17 13:26:50][WARN ][main][c.b.m.c.m.TableInfoHelper.initTableFields:342][]:Can not find table primary key in Class: "com.hp.settle.entity.ReportGoldConfig".
[2025-07-17 13:26:50][WARN ][main][c.b.m.c.i.DefaultSqlInjector.getMethodList:56][]:class com.hp.settle.entity.ReportGoldConfig ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[2025-07-17 13:26:50][INFO ][main][c.h.c.b.s.c.r.RedisConfig.redisTemplate:29][]:使用lettuce连接
[2025-07-17 13:26:51][INFO ][main][s.d.s.w.WebMvcPropertySourcedRequestMappingHandlerMapping.initHandlerMethods:69][]:Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
[2025-07-17 13:26:53][INFO ][main][o.s.c.c.u.InetUtils.convertAddress:170][]:Cannot determine local hostname
[2025-07-17 13:26:53][INFO ][main][o.a.j.l.DirectJDKLog.log:173][]:Starting ProtocolHandler ["http-nio-9232"]
[2025-07-17 13:26:55][INFO ][main][o.s.b.w.e.t.TomcatWebServer.start:220][]:Tomcat started on port(s): 9232 (http) with context path '/settleReport'
[2025-07-17 13:26:57][INFO ][main][o.s.c.c.u.InetUtils.convertAddress:170][]:Cannot determine local hostname
[2025-07-17 13:26:57][INFO ][main][s.d.s.w.p.DocumentationPluginsBootstrapper.start:93][]:Documentation plugins bootstrapped
[2025-07-17 13:26:57][INFO ][main][s.d.s.w.p.AbstractDocumentationPluginsBootstrapper.bootstrapDocumentationPlugins:79][]:Found 1 custom documentation plugin(s)
[2025-07-17 13:26:57][INFO ][main][s.d.s.w.s.ApiListingReferenceScanner.scan:44][]:Scanning for api listing references
[2025-07-17 13:26:57][INFO ][http-nio-9232-exec-1][o.a.j.r.showReport_jsp._jspService:205][]:第一次获取的rKey：10209
[2025-07-17 13:26:58][INFO ][main][o.s.b.StartupInfoLogger.logStarted:61][]:Started BossServiceSettleApplication in 16.858 seconds (JVM running for 18.302)
[2025-07-17 13:26:59][DEBUG][http-nio-9232-exec-1][o.a.i.l.j.BaseJdbcLogger.debug:137][]:==>  Preparing: select * from STLUDR.RVL_REPORT_RAQ where R_KEY = ? order by R_ID desc
[2025-07-17 13:26:59][DEBUG][http-nio-9232-exec-1][o.a.i.l.j.BaseJdbcLogger.debug:137][]:==> Parameters: 10209(String)
[2025-07-17 13:26:59][DEBUG][http-nio-9232-exec-1][o.a.i.l.j.BaseJdbcLogger.debug:137][]:<==      Total: 1
[2025-07-17 13:26:59][INFO ][http-nio-9232-exec-1][c.h.s.u.ReportUtil.getShowReportJspUrl:62][]:modifiedRpx: other/Sk_Line_Supp_srv6-3.rpx
[2025-07-17 13:26:59][DEBUG][http-nio-9232-exec-1][c.h.s.u.ReportUtil.getExcelName:107][]:-----------getExcelName()
[2025-07-17 13:26:59][DEBUG][http-nio-9232-exec-1][c.h.s.u.ReportUtil.getExcelName:108][]:rKey=10209
[2025-07-17 13:26:59][DEBUG][http-nio-9232-exec-1][c.h.s.u.ReportUtil.getExcelName:113][]:ce.getRaqName=双跨专线结算补充报表（SRv6-VPN智能专线）ADJUST-落地方分表
[2025-07-17 13:26:59][DEBUG][http-nio-9232-exec-1][o.a.i.l.j.BaseJdbcLogger.debug:137][]:==>  Preparing: SELECT T.DICTDESC FROM STLUDR.STL_CONF_DICT T WHERE T.DICTVALUE = ? AND T.ITEM = 'ENTITY'
[2025-07-17 13:26:59][DEBUG][http-nio-9232-exec-1][o.a.i.l.j.BaseJdbcLogger.debug:137][]:==> Parameters: null
[2025-07-17 13:26:59][DEBUG][http-nio-9232-exec-1][o.a.i.l.j.BaseJdbcLogger.debug:137][]:<==      Total: 0
[2025-07-17 13:26:59][DEBUG][http-nio-9232-exec-1][c.h.s.u.ReportUtil.getExcelName:130][]:settlemonth=202507
[2025-07-17 13:26:59][DEBUG][http-nio-9232-exec-1][c.h.s.u.ReportUtil.getExcelName:131][]:taxrate=6
[2025-07-17 13:26:59][DEBUG][http-nio-9232-exec-1][c.h.s.u.ReportUtil.getExcelName:132][]:entity=全部
[2025-07-17 13:26:59][DEBUG][http-nio-9232-exec-1][c.h.s.u.ReportUtil.getExcelName:133][]:income=业务总收入
[2025-07-17 13:26:59][DEBUG][http-nio-9232-exec-1][c.h.s.u.ReportUtil.getExcelName:134][]:in_out=结入
[2025-07-17 13:26:59][DEBUG][http-nio-9232-exec-1][c.h.s.u.ReportUtil.getExcelName:135][]:in_out=结入
[2025-07-17 13:26:59][INFO ][http-nio-9232-exec-1][c.h.s.u.ReportUtil.getExcelName:187][]:let me look look : 双跨专线结算补充报表（SRv6-VPN智能专线）-落地方分表
[2025-07-17 13:26:59][INFO ][http-nio-9232-exec-1][o.a.j.r.showReport_jsp._jspService:209][]:report:other/Sk_Line_Supp_srv6-3.rpx
[2025-07-17 13:26:59][INFO ][http-nio-9232-exec-1][o.a.j.r.showReport_jsp._jspService:210][]:excelName:双跨专线结算补充报表（SRv6-VPN智能专线）-落地方分表
[2025-07-17 13:26:59][INFO ][http-nio-9232-exec-1][o.a.j.r.showReport_jsp._jspService:237][]:请求参数：R_KEY=10209;tree_node_id=1068;staffNum=85195550005;
[2025-07-17 13:26:59][INFO ][http-nio-9232-exec-1][o.a.j.r.showReport_jsp._jspService:241][]:other/Sk_Line_Supp_srv6-3.rpx，是否有参数：true,参数报表：other/Sk_Line_Supp_srv6-3_arg.rpx
[2025-07-17 13:27:00][INFO ][http-nio-9232-exec-1][o.a.j.l.DirectJDKLog.log:173][]:Character decoding failed. Parameter [scale] with value [<%=scale %>] has been ignored. Note that the name and value quoted here may be corrupted due to the failed decoding. Use debug level logging to see the original, non-corrupted values.
 Note: further occurrences of Parameter errors will be logged at DEBUG level.
[2025-07-17 13:27:00][DEBUG][http-nio-9232-exec-1][o.a.i.l.j.BaseJdbcLogger.debug:137][]:==>  Preparing: select * from STLUDR.RVL_REPORT_RAQ where R_KEY = ? order by R_ID desc
[2025-07-17 13:27:00][DEBUG][http-nio-9232-exec-1][o.a.i.l.j.BaseJdbcLogger.debug:137][]:==> Parameters: 10209(String)
[2025-07-17 13:27:00][DEBUG][http-nio-9232-exec-1][o.a.i.l.j.BaseJdbcLogger.debug:137][]:<==      Total: 1
[2025-07-17 13:27:00][INFO ][http-nio-9232-exec-1][c.h.s.u.ReportUtil.getShowReportJspUrl:62][]:modifiedRpx: other/Sk_Line_Supp_srv6-3.rpx
[2025-07-17 13:27:07][INFO ][http-nio-9232-exec-2][c.h.s.c.AuthenticationFilter.doFilter:101][]:进入放行路径requestURLhttp://127.0.0.1:9232/settleReport/reportServlet
[2025-07-17 13:27:09][INFO ][http-nio-9232-exec-3][c.h.s.c.AuthenticationFilter.doFilter:101][]:进入放行路径requestURLhttp://127.0.0.1:9232/settleReport/reportServlet
[2025-07-17 13:27:09][INFO ][http-nio-9232-exec-3][c.h.s.r.ReportParamPostHandle.process:19][]:paramName:settlemonth---paramValue:202507
[2025-07-17 13:27:09][INFO ][http-nio-9232-exec-8][c.h.s.c.AuthenticationFilter.doFilter:159][]:进入 /reportJsp/queryReport.jsp
[2025-07-17 13:27:09][INFO ][http-nio-9232-exec-8][o.a.j.r.queryReport_jsp._jspService:169][]:context-path:/settleReport
[2025-07-17 13:27:09][INFO ][http-nio-9232-exec-8][o.a.j.r.queryReport_jsp._jspService:171][]:第3次获取的rKey：10209
[2025-07-17 13:27:09][INFO ][http-nio-9232-exec-8][o.a.j.r.queryReport_jsp._jspService:173][]:第4次获取的rKey1：10209
[2025-07-17 13:27:09][INFO ][http-nio-9232-exec-8][o.a.j.r.queryReport_jsp._jspService:179][]:联动报表4进来了
[2025-07-17 13:27:09][INFO ][http-nio-9232-exec-8][o.a.j.r.queryReport_jsp._jspService:181][]:联动报表4进来了获取的rKey：10209
[2025-07-17 13:27:09][INFO ][http-nio-9232-exec-8][o.a.j.r.queryReport_jsp._jspService:187][]:settlemonth:202507
[2025-07-17 13:27:09][DEBUG][http-nio-9232-exec-8][o.a.i.l.j.BaseJdbcLogger.debug:137][]:==>  Preparing: select * from STLUDR.RVL_REPORT_RAQ where R_KEY = ? and ? between START_PAYMENT and END_PAYMENT order by R_ID desc limit 1
[2025-07-17 13:27:09][DEBUG][http-nio-9232-exec-8][o.a.i.l.j.BaseJdbcLogger.debug:137][]:==> Parameters: 10209(String), 202507(String)
[2025-07-17 13:27:09][DEBUG][http-nio-9232-exec-8][o.a.i.l.j.BaseJdbcLogger.debug:137][]:<==      Total: 1
[2025-07-17 13:27:09][DEBUG][http-nio-9232-exec-8][c.h.s.u.ReportUtil.getExcelName:107][]:-----------getExcelName()
[2025-07-17 13:27:09][DEBUG][http-nio-9232-exec-8][c.h.s.u.ReportUtil.getExcelName:108][]:rKey=10209
[2025-07-17 13:27:09][DEBUG][http-nio-9232-exec-8][c.h.s.u.ReportUtil.getExcelName:113][]:ce.getRaqName=双跨专线结算补充报表（SRv6-VPN智能专线）ADJUST-落地方分表
[2025-07-17 13:27:09][DEBUG][http-nio-9232-exec-8][o.a.i.l.j.BaseJdbcLogger.debug:137][]:==>  Preparing: SELECT T.DICTDESC FROM STLUDR.STL_CONF_DICT T WHERE T.DICTVALUE = ? AND T.ITEM = 'ENTITY'
[2025-07-17 13:27:09][DEBUG][http-nio-9232-exec-8][o.a.i.l.j.BaseJdbcLogger.debug:137][]:==> Parameters: null
[2025-07-17 13:27:10][DEBUG][http-nio-9232-exec-8][o.a.i.l.j.BaseJdbcLogger.debug:137][]:<==      Total: 0
[2025-07-17 13:27:10][DEBUG][http-nio-9232-exec-8][c.h.s.u.ReportUtil.getExcelName:130][]:settlemonth=202507
[2025-07-17 13:27:10][DEBUG][http-nio-9232-exec-8][c.h.s.u.ReportUtil.getExcelName:131][]:taxrate=6
[2025-07-17 13:27:10][DEBUG][http-nio-9232-exec-8][c.h.s.u.ReportUtil.getExcelName:132][]:entity=全部
[2025-07-17 13:27:10][DEBUG][http-nio-9232-exec-8][c.h.s.u.ReportUtil.getExcelName:133][]:income=业务总收入
[2025-07-17 13:27:10][DEBUG][http-nio-9232-exec-8][c.h.s.u.ReportUtil.getExcelName:134][]:in_out=结入
[2025-07-17 13:27:10][DEBUG][http-nio-9232-exec-8][c.h.s.u.ReportUtil.getExcelName:135][]:in_out=结入
[2025-07-17 13:27:10][DEBUG][http-nio-9232-exec-8][o.a.i.l.j.BaseJdbcLogger.debug:137][]:==>  Preparing: SELECT t.prov_nm FROM stludr.stl_province_cd t WHERE t.prov_cd= ? limit 1
[2025-07-17 13:27:10][DEBUG][http-nio-9232-exec-8][o.a.i.l.j.BaseJdbcLogger.debug:137][]:==> Parameters: 100(String)
[2025-07-17 13:27:10][DEBUG][http-nio-9232-exec-8][o.a.i.l.j.BaseJdbcLogger.debug:137][]:<==      Total: 1
[2025-07-17 13:27:10][INFO ][http-nio-9232-exec-8][c.h.s.u.ReportUtil.getExcelName:187][]:let me look look : 双跨专线结算补充报表（SRv6-VPN智能专线）-落地方分表
[2025-07-17 13:27:10][INFO ][http-nio-9232-exec-8][o.a.j.r.queryReport_jsp._jspService:194][]:从show获取到得report:other/Sk_Line_Supp_srv6-3.rpx
[2025-07-17 13:27:10][INFO ][http-nio-9232-exec-8][o.a.j.r.queryReport_jsp._jspService:201][]:报表名称：双跨专线结算补充报表（SRv6-VPN智能专线）-落地方分表，报表路径：other/Sk_Line_Supp_srv6-3.rpx，是否使用缓存:no
[2025-07-17 13:27:10][DEBUG][http-nio-9232-exec-8][o.a.i.l.j.BaseJdbcLogger.debug:137][]:==>  Preparing: SELECT decode(count(1), 0, 'N', 'Y') AS dictvalue, '' AS dictdesc FROM stludr.am_tree_check_new a WHERE a.TREE_NODE_ID = ? AND a.STATE = 'Y' AND a.ACCT_MONTH = ?
[2025-07-17 13:27:10][DEBUG][http-nio-9232-exec-8][o.a.i.l.j.BaseJdbcLogger.debug:137][]:==> Parameters: 1068(String), 202507(String)
[2025-07-17 13:27:10][DEBUG][http-nio-9232-exec-8][o.a.i.l.j.BaseJdbcLogger.debug:137][]:<==      Total: 1
[2025-07-17 13:27:10][INFO ][http-nio-9232-exec-8][c.h.s.s.i.ReportCheckServiceImpl.isCheck:69][]:******报表审核通过，本报表的编号=[1068]******
[2025-07-17 13:27:39][DEBUG][http-nio-9232-exec-8][o.a.i.l.j.BaseJdbcLogger.debug:137][]:==>  Preparing: SELECT RAQ_KEY,APPROVE_FLAG,GOLD_BANK_CODE FROM report_gold_config WHERE (APPROVE_FLAG = ? AND RAQ_KEY = ?)
[2025-07-17 13:27:39][DEBUG][http-nio-9232-exec-8][o.a.i.l.j.BaseJdbcLogger.debug:137][]:==> Parameters: Y(String), 10209(String)
[2025-07-17 13:27:39][ERROR][http-nio-9232-exec-8][o.a.j.l.DirectJDKLog.log:175][]:Servlet.service() for servlet [jsp] threw exception
java.sql.SQLSyntaxErrorException: Unknown column 'GOLD_BANK_CODE' in 'field list'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:120)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:370)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:497)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59)
	at com.sun.proxy.$Proxy142.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:64)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:79)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:63)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:89)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	at com.sun.proxy.$Proxy93.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:166)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy118.selectList(Unknown Source)
	at com.baomidou.mybatisplus.core.mapper.BaseMapper.selectOne(BaseMapper.java:173)
	at java.lang.invoke.MethodHandle.invokeWithArguments(MethodHandle.java:627)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$DefaultMethodInvoker.invoke(MybatisMapperProxy.java:162)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy118.selectOne(Unknown Source)
	at com.baomidou.mybatisplus.extension.service.impl.ServiceImpl.getOne(ServiceImpl.java:202)
	at com.baomidou.mybatisplus.extension.service.IService.getOne(IService.java:320)
	at com.hp.settle.service.impl.ReportGoldApproveImpl.isApprove(ReportGoldApproveImpl.java:49)
	at com.hp.settle.service.impl.ReportGoldApproveImpl$$FastClassBySpringCGLIB$$baea858e.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386)
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704)
	at com.hp.settle.service.impl.ReportGoldApproveImpl$$EnhancerBySpringCGLIB$$a2662c22.isApprove(<generated>)
	at com.hp.settle.util.ReportUtil.isApprove(ReportUtil.java:406)
	at org.apache.jsp.reportJsp.queryReport_jsp._jspService(queryReport_jsp.java:218)
	at org.apache.jasper.runtime.HttpJspBase.service(HttpJspBase.java:70)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:750)
	at org.apache.jasper.servlet.JspServletWrapper.service(JspServletWrapper.java:466)
	at org.apache.jasper.servlet.JspServlet.serviceJspFile(JspServlet.java:379)
	at org.apache.jasper.servlet.JspServlet.service(JspServlet.java:327)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:750)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.ApplicationDispatcher.invoke(ApplicationDispatcher.java:711)
	at org.apache.catalina.core.ApplicationDispatcher.processRequest(ApplicationDispatcher.java:459)
	at org.apache.catalina.core.ApplicationDispatcher.doForward(ApplicationDispatcher.java:385)
	at org.apache.catalina.core.ApplicationDispatcher.forward(ApplicationDispatcher.java:313)
	at com.hp.settle.config.AuthenticationFilter.doFilter(AuthenticationFilter.java:160)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:177)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:891)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1784)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:745)
[2025-07-17 13:27:39][ERROR][http-nio-9232-exec-8][o.a.j.l.DirectJDKLog.log:175][]:Servlet.service() for servlet [jsp] in context with path [/settleReport] threw exception [org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column 'GOLD_BANK_CODE' in 'field list'
### The error may exist in com/hp/settle/mapper/ReportGoldApproveMapper.java (best guess)
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT  RAQ_KEY,APPROVE_FLAG,GOLD_BANK_CODE  FROM report_gold_config     WHERE (APPROVE_FLAG = ? AND RAQ_KEY = ?)
### Cause: java.sql.SQLSyntaxErrorException: Unknown column 'GOLD_BANK_CODE' in 'field list'
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'GOLD_BANK_CODE' in 'field list'] with root cause
java.sql.SQLSyntaxErrorException: Unknown column 'GOLD_BANK_CODE' in 'field list'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:120)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:370)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:497)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59)
	at com.sun.proxy.$Proxy142.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:64)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:79)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:63)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:89)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	at com.sun.proxy.$Proxy93.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:166)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy118.selectList(Unknown Source)
	at com.baomidou.mybatisplus.core.mapper.BaseMapper.selectOne(BaseMapper.java:173)
	at java.lang.invoke.MethodHandle.invokeWithArguments(MethodHandle.java:627)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$DefaultMethodInvoker.invoke(MybatisMapperProxy.java:162)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy118.selectOne(Unknown Source)
	at com.baomidou.mybatisplus.extension.service.impl.ServiceImpl.getOne(ServiceImpl.java:202)
	at com.baomidou.mybatisplus.extension.service.IService.getOne(IService.java:320)
	at com.hp.settle.service.impl.ReportGoldApproveImpl.isApprove(ReportGoldApproveImpl.java:49)
	at com.hp.settle.service.impl.ReportGoldApproveImpl$$FastClassBySpringCGLIB$$baea858e.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386)
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704)
	at com.hp.settle.service.impl.ReportGoldApproveImpl$$EnhancerBySpringCGLIB$$a2662c22.isApprove(<generated>)
	at com.hp.settle.util.ReportUtil.isApprove(ReportUtil.java:406)
	at org.apache.jsp.reportJsp.queryReport_jsp._jspService(queryReport_jsp.java:218)
	at org.apache.jasper.runtime.HttpJspBase.service(HttpJspBase.java:70)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:750)
	at org.apache.jasper.servlet.JspServletWrapper.service(JspServletWrapper.java:466)
	at org.apache.jasper.servlet.JspServlet.serviceJspFile(JspServlet.java:379)
	at org.apache.jasper.servlet.JspServlet.service(JspServlet.java:327)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:750)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.ApplicationDispatcher.invoke(ApplicationDispatcher.java:711)
	at org.apache.catalina.core.ApplicationDispatcher.processRequest(ApplicationDispatcher.java:459)
	at org.apache.catalina.core.ApplicationDispatcher.doForward(ApplicationDispatcher.java:385)
	at org.apache.catalina.core.ApplicationDispatcher.forward(ApplicationDispatcher.java:313)
	at com.hp.settle.config.AuthenticationFilter.doFilter(AuthenticationFilter.java:160)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:177)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:891)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1784)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:745)
[2025-07-17 13:27:39][INFO ][http-nio-9232-exec-8][o.a.j.l.DirectJDKLog.log:173][]:Initializing Spring DispatcherServlet 'dispatcherServlet'
[2025-07-17 13:27:39][INFO ][http-nio-9232-exec-8][o.s.w.s.FrameworkServlet.initServletBean:525][]:Initializing Servlet 'dispatcherServlet'
[2025-07-17 13:27:39][INFO ][http-nio-9232-exec-8][o.s.w.s.FrameworkServlet.initServletBean:547][]:Completed initialization in 1 ms
[2025-07-17 13:28:04][INFO ][main][o.s.b.SpringApplication.logStartupProfileInfo:637][]:The following 1 profile is active: "dev"
[2025-07-17 13:28:05][INFO ][main][o.s.d.r.c.RepositoryConfigurationDelegate.multipleStoresDetected:262][]:Multiple Spring Data modules found, entering strict repository configuration mode
[2025-07-17 13:28:05][INFO ][main][o.s.d.r.c.RepositoryConfigurationDelegate.registerRepositoriesIn:132][]:Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[2025-07-17 13:28:05][INFO ][main][o.s.d.r.c.RepositoryConfigurationDelegate.registerRepositoriesIn:201][]:Finished Spring Data repository scanning in 15 ms. Found 0 Redis repository interfaces.
[2025-07-17 13:28:05][INFO ][main][o.s.c.c.s.GenericScope.setSerializationId:283][]:BeanFactory id=dbe784e5-818f-3f46-bd86-89a8e46f5497
[2025-07-17 13:28:05][INFO ][main][c.u.j.c.EnableEncryptablePropertiesBeanFactoryPostProcessor.postProcessBeanFactory:39][]:Post-processing PropertySource instances
[2025-07-17 13:28:05][INFO ][main][c.u.j.EncryptablePropertySourceConverter.makeEncryptable:56][]:Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[2025-07-17 13:28:05][INFO ][main][c.u.j.EncryptablePropertySourceConverter.makeEncryptable:56][]:Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[2025-07-17 13:28:05][INFO ][main][c.u.j.EncryptablePropertySourceConverter.makeEncryptable:56][]:Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[2025-07-17 13:28:05][INFO ][main][c.u.j.EncryptablePropertySourceConverter.makeEncryptable:56][]:Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[2025-07-17 13:28:05][INFO ][main][c.u.j.EncryptablePropertySourceConverter.makeEncryptable:56][]:Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[2025-07-17 13:28:05][INFO ][main][c.u.j.EncryptablePropertySourceConverter.makeEncryptable:56][]:Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[2025-07-17 13:28:05][INFO ][main][c.u.j.EncryptablePropertySourceConverter.makeEncryptable:56][]:Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[2025-07-17 13:28:05][INFO ][main][c.u.j.EncryptablePropertySourceConverter.makeEncryptable:56][]:Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[2025-07-17 13:28:05][INFO ][main][c.u.j.EncryptablePropertySourceConverter.makeEncryptable:56][]:Converting PropertySource Config resource 'class path resource [config/application-dev.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[2025-07-17 13:28:05][INFO ][main][c.u.j.EncryptablePropertySourceConverter.makeEncryptable:56][]:Converting PropertySource Config resource 'class path resource [config/application.yml]' via location 'optional:classpath:/config/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[2025-07-17 13:28:05][INFO ][main][c.u.j.EncryptablePropertySourceConverter.makeEncryptable:56][]:Converting PropertySource Config resource 'class path resource [application.properties]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[2025-07-17 13:28:05][INFO ][main][c.u.j.EncryptablePropertySourceConverter.makeEncryptable:56][]:Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[2025-07-17 13:28:05][INFO ][main][c.u.j.EncryptablePropertySourceConverter.makeEncryptable:56][]:Converting PropertySource defaultProperties [org.springframework.boot.DefaultPropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[2025-07-17 13:28:05][INFO ][main][c.u.j.f.DefaultLazyPropertyFilter.lambda$null$2:31][]:Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[2025-07-17 13:28:05][INFO ][main][c.u.j.r.DefaultLazyPropertyResolver.lambda$null$2:35][]:Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[2025-07-17 13:28:05][INFO ][main][c.u.j.d.DefaultLazyPropertyDetector.lambda$null$2:35][]:Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[2025-07-17 13:28:05][DEBUG][main][o.s.b.w.s.s.DocumentRoot.getArchiveFileDocumentRoot:81][]:Code archive: D:\04server\apache-maven-3.6.2\repository3\org\springframework\boot\spring-boot\2.7.6\spring-boot-2.7.6.jar
[2025-07-17 13:28:05][DEBUG][main][o.s.b.w.s.s.DocumentRoot.getExplodedWarFileDocumentRoot:125][]:Code archive: D:\04server\apache-maven-3.6.2\repository3\org\springframework\boot\spring-boot\2.7.6\spring-boot-2.7.6.jar
[2025-07-17 13:28:05][DEBUG][main][o.s.b.w.s.s.DocumentRoot.getValidDirectory:69][]:Document root: D:\05code\newsettle\settle-service-report\src\main\webapp
[2025-07-17 13:28:06][INFO ][main][o.s.b.w.e.t.TomcatWebServer.initialize:108][]:Tomcat initialized with port(s): 9232 (http)
[2025-07-17 13:28:06][INFO ][main][o.a.j.l.DirectJDKLog.log:173][]:Initializing ProtocolHandler ["http-nio-9232"]
[2025-07-17 13:28:06][INFO ][main][o.a.j.l.DirectJDKLog.log:173][]:Starting service [Tomcat]
[2025-07-17 13:28:06][INFO ][main][o.a.j.l.DirectJDKLog.log:173][]:Starting Servlet engine: [Apache Tomcat/9.0.69]
[2025-07-17 13:28:06][INFO ][main][o.a.j.l.DirectJDKLog.log:173][]:At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
[2025-07-17 13:28:06][INFO ][main][o.a.j.l.DirectJDKLog.log:173][]:Initializing Spring embedded WebApplicationContext
[2025-07-17 13:28:06][INFO ][main][o.s.b.w.s.c.ServletWebServerApplicationContext.prepareWebApplicationContext:292][]:Root WebApplicationContext: initialization completed in 1988 ms
[2025-07-17 13:28:06][INFO ][main][c.h.s.c.ReportWebConfig.servletRegistrationBean:30][]:NACOS_SERVER:null
[2025-07-17 13:28:06][INFO ][main][c.h.s.c.ReportWebConfig.servletRegistrationBean:38][]:开发环境部署-配置文件：raqsoftConfig.xml
[2025-07-17 13:28:06][INFO ][main][c.a.d.s.b.a.DruidDataSourceAutoConfigure.dataSource:56][]:Init DruidDataSource
[2025-07-17 13:28:06][INFO ][main][c.a.d.p.DruidDataSource.init:985][]:{dataSource-1} inited
[2025-07-17 13:28:08][INFO ][main][c.h.c.b.s.c.r.RedisPoolConfiguration.redisPoolConfig:279][]:redis 连接池配置
[2025-07-17 13:28:08][INFO ][main][c.h.c.b.s.c.r.RedisPoolConfiguration.redisConnectionFactory:94][]:redis factory 加载
[2025-07-17 13:28:08][INFO ][main][c.h.c.b.s.c.r.RedisPoolConfiguration.getRedisStandaloneConfiguration:230][]:redis 单机配置
[2025-07-17 13:28:08][WARN ][main][c.b.m.c.m.TableInfoHelper.initTableFields:342][]:Can not find table primary key in Class: "com.hp.settle.entity.ReportGoldConfig".
[2025-07-17 13:28:08][WARN ][main][c.b.m.c.i.DefaultSqlInjector.getMethodList:56][]:class com.hp.settle.entity.ReportGoldConfig ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[2025-07-17 13:28:09][INFO ][main][c.h.c.b.s.c.r.RedisConfig.redisTemplate:29][]:使用lettuce连接
[2025-07-17 13:28:10][INFO ][main][s.d.s.w.WebMvcPropertySourcedRequestMappingHandlerMapping.initHandlerMethods:69][]:Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
[2025-07-17 13:28:12][INFO ][main][o.s.c.c.u.InetUtils.convertAddress:170][]:Cannot determine local hostname
[2025-07-17 13:28:12][INFO ][main][o.a.j.l.DirectJDKLog.log:173][]:Starting ProtocolHandler ["http-nio-9232"]
[2025-07-17 13:28:14][INFO ][main][o.s.b.w.e.t.TomcatWebServer.start:220][]:Tomcat started on port(s): 9232 (http) with context path '/settleReport'
[2025-07-17 13:28:15][INFO ][main][o.s.c.c.u.InetUtils.convertAddress:170][]:Cannot determine local hostname
[2025-07-17 13:28:15][INFO ][main][s.d.s.w.p.DocumentationPluginsBootstrapper.start:93][]:Documentation plugins bootstrapped
[2025-07-17 13:28:15][INFO ][main][s.d.s.w.p.AbstractDocumentationPluginsBootstrapper.bootstrapDocumentationPlugins:79][]:Found 1 custom documentation plugin(s)
[2025-07-17 13:28:15][INFO ][main][s.d.s.w.s.ApiListingReferenceScanner.scan:44][]:Scanning for api listing references
[2025-07-17 13:28:16][INFO ][main][o.s.b.StartupInfoLogger.logStarted:61][]:Started BossServiceSettleApplication in 15.557 seconds (JVM running for 16.877)
[2025-07-17 13:28:23][INFO ][http-nio-9232-exec-1][o.a.j.r.showReport_jsp._jspService:205][]:第一次获取的rKey：10209
[2025-07-17 13:28:25][DEBUG][http-nio-9232-exec-1][o.a.i.l.j.BaseJdbcLogger.debug:137][]:==>  Preparing: select * from STLUDR.RVL_REPORT_RAQ where R_KEY = ? order by R_ID desc
[2025-07-17 13:28:25][DEBUG][http-nio-9232-exec-1][o.a.i.l.j.BaseJdbcLogger.debug:137][]:==> Parameters: 10209(String)
[2025-07-17 13:28:25][DEBUG][http-nio-9232-exec-1][o.a.i.l.j.BaseJdbcLogger.debug:137][]:<==      Total: 1
[2025-07-17 13:28:25][INFO ][http-nio-9232-exec-1][c.h.s.u.ReportUtil.getShowReportJspUrl:61][]:modifiedRpx: other/Sk_Line_Supp_srv6-3.rpx
[2025-07-17 13:28:25][DEBUG][http-nio-9232-exec-1][c.h.s.u.ReportUtil.getExcelName:106][]:-----------getExcelName()
[2025-07-17 13:28:25][DEBUG][http-nio-9232-exec-1][c.h.s.u.ReportUtil.getExcelName:107][]:rKey=10209
[2025-07-17 13:28:25][DEBUG][http-nio-9232-exec-1][c.h.s.u.ReportUtil.getExcelName:112][]:ce.getRaqName=双跨专线结算补充报表（SRv6-VPN智能专线）ADJUST-落地方分表
[2025-07-17 13:28:25][DEBUG][http-nio-9232-exec-1][o.a.i.l.j.BaseJdbcLogger.debug:137][]:==>  Preparing: SELECT T.DICTDESC FROM STLUDR.STL_CONF_DICT T WHERE T.DICTVALUE = ? AND T.ITEM = 'ENTITY'
[2025-07-17 13:28:25][DEBUG][http-nio-9232-exec-1][o.a.i.l.j.BaseJdbcLogger.debug:137][]:==> Parameters: null
[2025-07-17 13:28:25][DEBUG][http-nio-9232-exec-1][o.a.i.l.j.BaseJdbcLogger.debug:137][]:<==      Total: 0
[2025-07-17 13:28:25][DEBUG][http-nio-9232-exec-1][c.h.s.u.ReportUtil.getExcelName:129][]:settlemonth=202507
[2025-07-17 13:28:25][DEBUG][http-nio-9232-exec-1][c.h.s.u.ReportUtil.getExcelName:130][]:taxrate=6
[2025-07-17 13:28:25][DEBUG][http-nio-9232-exec-1][c.h.s.u.ReportUtil.getExcelName:131][]:entity=全部
[2025-07-17 13:28:25][DEBUG][http-nio-9232-exec-1][c.h.s.u.ReportUtil.getExcelName:132][]:income=业务总收入
[2025-07-17 13:28:25][DEBUG][http-nio-9232-exec-1][c.h.s.u.ReportUtil.getExcelName:133][]:in_out=结入
[2025-07-17 13:28:25][DEBUG][http-nio-9232-exec-1][c.h.s.u.ReportUtil.getExcelName:134][]:in_out=结入
[2025-07-17 13:28:25][INFO ][http-nio-9232-exec-1][c.h.s.u.ReportUtil.getExcelName:186][]:let me look look : 双跨专线结算补充报表（SRv6-VPN智能专线）-落地方分表
[2025-07-17 13:28:25][INFO ][http-nio-9232-exec-1][o.a.j.r.showReport_jsp._jspService:209][]:report:other/Sk_Line_Supp_srv6-3.rpx
[2025-07-17 13:28:25][INFO ][http-nio-9232-exec-1][o.a.j.r.showReport_jsp._jspService:210][]:excelName:双跨专线结算补充报表（SRv6-VPN智能专线）-落地方分表
[2025-07-17 13:28:25][INFO ][http-nio-9232-exec-1][o.a.j.r.showReport_jsp._jspService:237][]:请求参数：R_KEY=10209;tree_node_id=1068;staffNum=85195550005;
[2025-07-17 13:28:25][INFO ][http-nio-9232-exec-1][o.a.j.r.showReport_jsp._jspService:241][]:other/Sk_Line_Supp_srv6-3.rpx，是否有参数：true,参数报表：other/Sk_Line_Supp_srv6-3_arg.rpx
[2025-07-17 13:28:26][INFO ][http-nio-9232-exec-1][o.a.j.l.DirectJDKLog.log:173][]:Character decoding failed. Parameter [scale] with value [<%=scale %>] has been ignored. Note that the name and value quoted here may be corrupted due to the failed decoding. Use debug level logging to see the original, non-corrupted values.
 Note: further occurrences of Parameter errors will be logged at DEBUG level.
[2025-07-17 13:28:26][DEBUG][http-nio-9232-exec-1][o.a.i.l.j.BaseJdbcLogger.debug:137][]:==>  Preparing: select * from STLUDR.RVL_REPORT_RAQ where R_KEY = ? order by R_ID desc
[2025-07-17 13:28:26][DEBUG][http-nio-9232-exec-1][o.a.i.l.j.BaseJdbcLogger.debug:137][]:==> Parameters: 10209(String)
[2025-07-17 13:28:26][DEBUG][http-nio-9232-exec-1][o.a.i.l.j.BaseJdbcLogger.debug:137][]:<==      Total: 1
[2025-07-17 13:28:26][INFO ][http-nio-9232-exec-1][c.h.s.u.ReportUtil.getShowReportJspUrl:61][]:modifiedRpx: other/Sk_Line_Supp_srv6-3.rpx
[2025-07-17 13:28:33][INFO ][http-nio-9232-exec-2][c.h.s.c.AuthenticationFilter.doFilter:101][]:进入放行路径requestURLhttp://127.0.0.1:9232/settleReport/reportServlet
[2025-07-17 13:28:35][INFO ][http-nio-9232-exec-3][c.h.s.c.AuthenticationFilter.doFilter:101][]:进入放行路径requestURLhttp://127.0.0.1:9232/settleReport/reportServlet
[2025-07-17 13:28:35][INFO ][http-nio-9232-exec-3][c.h.s.r.ReportParamPostHandle.process:19][]:paramName:settlemonth---paramValue:202507
[2025-07-17 13:28:35][INFO ][http-nio-9232-exec-4][c.h.s.c.AuthenticationFilter.doFilter:159][]:进入 /reportJsp/queryReport.jsp
[2025-07-17 13:28:35][INFO ][http-nio-9232-exec-4][o.a.j.r.queryReport_jsp._jspService:169][]:context-path:/settleReport
[2025-07-17 13:28:35][INFO ][http-nio-9232-exec-4][o.a.j.r.queryReport_jsp._jspService:171][]:第3次获取的rKey：10209
[2025-07-17 13:28:35][INFO ][http-nio-9232-exec-4][o.a.j.r.queryReport_jsp._jspService:173][]:第4次获取的rKey1：10209
[2025-07-17 13:28:35][INFO ][http-nio-9232-exec-4][o.a.j.r.queryReport_jsp._jspService:179][]:联动报表4进来了
[2025-07-17 13:28:35][INFO ][http-nio-9232-exec-4][o.a.j.r.queryReport_jsp._jspService:181][]:联动报表4进来了获取的rKey：10209
[2025-07-17 13:28:35][INFO ][http-nio-9232-exec-4][o.a.j.r.queryReport_jsp._jspService:187][]:settlemonth:202507
[2025-07-17 13:28:35][DEBUG][http-nio-9232-exec-4][o.a.i.l.j.BaseJdbcLogger.debug:137][]:==>  Preparing: select * from STLUDR.RVL_REPORT_RAQ where R_KEY = ? and ? between START_PAYMENT and END_PAYMENT order by R_ID desc limit 1
[2025-07-17 13:28:35][DEBUG][http-nio-9232-exec-4][o.a.i.l.j.BaseJdbcLogger.debug:137][]:==> Parameters: 10209(String), 202507(String)
[2025-07-17 13:28:35][DEBUG][http-nio-9232-exec-4][o.a.i.l.j.BaseJdbcLogger.debug:137][]:<==      Total: 1
[2025-07-17 13:28:35][DEBUG][http-nio-9232-exec-4][c.h.s.u.ReportUtil.getExcelName:106][]:-----------getExcelName()
[2025-07-17 13:28:35][DEBUG][http-nio-9232-exec-4][c.h.s.u.ReportUtil.getExcelName:107][]:rKey=10209
[2025-07-17 13:28:35][DEBUG][http-nio-9232-exec-4][c.h.s.u.ReportUtil.getExcelName:112][]:ce.getRaqName=双跨专线结算补充报表（SRv6-VPN智能专线）ADJUST-落地方分表
[2025-07-17 13:28:35][DEBUG][http-nio-9232-exec-4][o.a.i.l.j.BaseJdbcLogger.debug:137][]:==>  Preparing: SELECT T.DICTDESC FROM STLUDR.STL_CONF_DICT T WHERE T.DICTVALUE = ? AND T.ITEM = 'ENTITY'
[2025-07-17 13:28:35][DEBUG][http-nio-9232-exec-4][o.a.i.l.j.BaseJdbcLogger.debug:137][]:==> Parameters: null
[2025-07-17 13:28:35][DEBUG][http-nio-9232-exec-4][o.a.i.l.j.BaseJdbcLogger.debug:137][]:<==      Total: 0
[2025-07-17 13:28:35][DEBUG][http-nio-9232-exec-4][c.h.s.u.ReportUtil.getExcelName:129][]:settlemonth=202507
[2025-07-17 13:28:35][DEBUG][http-nio-9232-exec-4][c.h.s.u.ReportUtil.getExcelName:130][]:taxrate=6
[2025-07-17 13:28:35][DEBUG][http-nio-9232-exec-4][c.h.s.u.ReportUtil.getExcelName:131][]:entity=全部
[2025-07-17 13:28:35][DEBUG][http-nio-9232-exec-4][c.h.s.u.ReportUtil.getExcelName:132][]:income=业务总收入
[2025-07-17 13:28:35][DEBUG][http-nio-9232-exec-4][c.h.s.u.ReportUtil.getExcelName:133][]:in_out=结入
[2025-07-17 13:28:35][DEBUG][http-nio-9232-exec-4][c.h.s.u.ReportUtil.getExcelName:134][]:in_out=结入
[2025-07-17 13:28:35][DEBUG][http-nio-9232-exec-4][o.a.i.l.j.BaseJdbcLogger.debug:137][]:==>  Preparing: SELECT t.prov_nm FROM stludr.stl_province_cd t WHERE t.prov_cd= ? limit 1
[2025-07-17 13:28:35][DEBUG][http-nio-9232-exec-4][o.a.i.l.j.BaseJdbcLogger.debug:137][]:==> Parameters: 100(String)
[2025-07-17 13:28:36][DEBUG][http-nio-9232-exec-4][o.a.i.l.j.BaseJdbcLogger.debug:137][]:<==      Total: 1
[2025-07-17 13:28:36][INFO ][http-nio-9232-exec-4][c.h.s.u.ReportUtil.getExcelName:186][]:let me look look : 双跨专线结算补充报表（SRv6-VPN智能专线）-落地方分表
[2025-07-17 13:28:36][INFO ][http-nio-9232-exec-4][o.a.j.r.queryReport_jsp._jspService:194][]:从show获取到得report:other/Sk_Line_Supp_srv6-3.rpx
[2025-07-17 13:28:36][INFO ][http-nio-9232-exec-4][o.a.j.r.queryReport_jsp._jspService:201][]:报表名称：双跨专线结算补充报表（SRv6-VPN智能专线）-落地方分表，报表路径：other/Sk_Line_Supp_srv6-3.rpx，是否使用缓存:no
[2025-07-17 13:28:36][DEBUG][http-nio-9232-exec-4][o.a.i.l.j.BaseJdbcLogger.debug:137][]:==>  Preparing: SELECT decode(count(1), 0, 'N', 'Y') AS dictvalue, '' AS dictdesc FROM stludr.am_tree_check_new a WHERE a.TREE_NODE_ID = ? AND a.STATE = 'Y' AND a.ACCT_MONTH = ?
[2025-07-17 13:28:36][DEBUG][http-nio-9232-exec-4][o.a.i.l.j.BaseJdbcLogger.debug:137][]:==> Parameters: 1068(String), 202507(String)
[2025-07-17 13:28:36][DEBUG][http-nio-9232-exec-4][o.a.i.l.j.BaseJdbcLogger.debug:137][]:<==      Total: 1
[2025-07-17 13:28:36][INFO ][http-nio-9232-exec-4][c.h.s.s.i.ReportCheckServiceImpl.isCheck:69][]:******报表审核通过，本报表的编号=[1068]******
