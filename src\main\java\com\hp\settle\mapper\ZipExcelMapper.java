package com.hp.settle.mapper;

import com.hp.settle.vo.DictModelVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface ZipExcelMapper {

    @Select("SELECT T.DICTVALUE, T.DICTDESC FROM STLUDR.STL_CONF_DICT T " +
            "WHERE TO_DATE(#{settleMonth}, 'yyyyMM') BETWEEN TO_DATE(TO_CHAR(T.EFFECTIVE_DATE, 'yyyyMM'), 'yyyyMM') " +
            "AND TO_DATE(TO_CHAR(T.EXPIRY_DATE, 'yyyyMM'), 'yyyyMM') " +
            "AND T.ITEM = #{taxrate} AND t.remark = #{bArbl} ORDER BY T.DICTORDER ASC")
    List<DictModelVo> getTaxrate(@Param("settleMonth") String settleMonth,
                                 @Param("taxrate") String taxrate,
                                 @Param("bArbl") String bArbl);

    @Select("SELECT T.T_MONTH AS DICTVALUE, '' AS DICTDESC FROM STLUDR.RVL_DETAULT_MONTH T " +
            "WHERE T.T_SETTMODE = #{bArbl} AND T.T_NEW = 'Y' ")
    List<DictModelVo> getDefaultMonth(String bArbl);

    @Select("SELECT T.DICTVALUE, T.DICTDESC FROM STLUDR.STL_CONF_DICT T WHERE TO_DATE(#{settleMonth}, 'yyyyMM') " +
            "BETWEEN TO_DATE(TO_CHAR(T.EFFECTIVE_DATE, 'yyyyMM'), 'yyyyMM') " +
            "AND TO_DATE(TO_CHAR(T.EXPIRY_DATE, 'yyyyMM'), 'yyyyMM') " +
            "AND T.ITEM = #{taxratex} ORDER BY T.DICTORDER ASC")
    List<DictModelVo> getOption(@Param("settleMonth") String settleMonth,
                                @Param("taxratex") String taxratex);

    @Select("SELECT T.DICTVALUE, T.DICTDESC FROM STLUDR.STL_CONF_DICT T WHERE T.ITEM = 'ENTITY'")
    List<DictModelVo> getEntityList(String s);

    @Select("SELECT T.PROV_CD AS DICTVALUE, T.PROV_NM AS DICTDESC FROM STLUDR.STL_PROVINCE_CD T where t.prov_cd <> '030' ORDER BY T.PROV_CD ASC")
    List<DictModelVo> getProductList1(String y);

    @Select("SELECT T.PROV_CD AS DICTVALUE, T.PROV_NM AS DICTDESC FROM STLUDR.STL_PROVINCE_CD T WHERE T.PROV_CD <> '000' and t.prov_cd <> '030' ORDER BY T.PROV_CD ASC")
    List<DictModelVo> getProductList2(String n);

    @Select("SELECT T.PROV_CD AS DICTVALUE, T.PROV_NM AS DICTDESC FROM STLUDR.STL_PROVINCE_CD T ORDER BY T.PROV_CD ASC ")
    List<DictModelVo> getProductList3(String y030);

    @Select("SELECT T.PROV_CD AS DICTVALUE, T.PROV_NM AS DICTDESC FROM STLUDR.STL_PROVINCE_CD T WHERE PROV_TYPE = '0' ORDER BY T.PROV_CD ASC ")
    List<DictModelVo> getProductList4(String pure);

    @Select("SELECT T.DICTDESC FROM STLUDR.STL_CONF_DICT T WHERE T.DICTVALUE = #{entity} AND T.ITEM = 'ENTITY' ")
    String getEntity(String entity);

    @Select("SELECT  t.prov_nm FROM  stludr.stl_province_cd t  WHERE t.prov_cd= #{prov} limit 1")
    String getProvName(String prov);

    @Update("update STLUDR.STL_CONF_DICT set DICTVALUE = #{flag} where ITEM='SPECIAL_LINE' " )
    void updateStlConfigFlag(String flag);

}
