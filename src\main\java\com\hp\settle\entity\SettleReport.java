package com.hp.settle.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class SettleReport {

    @JsonProperty(value = "id")
    private String id;
    @JsonProperty(value = "pId")
    private String pId;
    @JsonProperty(value = "name")
    private String name;
    @JsonProperty(value = "ourl")
    private String url;
    @JsonProperty(value = "tree_node_id")
    private String tree_node_id;


    public SettleReport(String id, String pId, String name, String url, String tree_node_id) {
        this.id = id;
        this.pId = pId;
        this.name = name;
        this.url =  url;
        this.tree_node_id = tree_node_id;
    }

    @Override
    public String toString() {
        return "SettleReport{" +
                "id='" + id + '\'' +
                ", pId='" + pId + '\'' +
                ", name='" + name + '\'' +
                ", url='" + url + '\'' +
                ", tree_node_id='" + tree_node_id + '\'' +
                '}';
    }
}
