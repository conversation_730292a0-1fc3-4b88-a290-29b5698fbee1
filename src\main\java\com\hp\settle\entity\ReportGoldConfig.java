package com.hp.settle.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName(value = "report_gold_config")
public class ReportGoldConfig {

    @TableField("RAQ_KEY")
    private String raqKey;

    @TableField("APPROVE_FLAG")
    private String approveFlag;

    @TableField("GOLD_BANK_CODE")
    private String goldBankCode;
}
