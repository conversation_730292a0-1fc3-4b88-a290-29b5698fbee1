package com.hp.settle.config;

import com.hp.cmcc.bboss.settle.util.RedisUtil;
import com.hp.settle.constants.GoldBankConstants;
import com.hp.settle.mapper.ReportCheckMapper;
import com.hp.settle.util.DataDecryptor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.*;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;

/**
 * Project: boss-service-runqianreport
 * Date : 2023/11/15 14:48
 * Author : hw
 * Description : 过滤并处理请求
 */
@Component
@Slf4j
public class AuthenticationFilter implements Filter {
    @Resource
    private ReportCheckMapper reportCheckMapper;

    @Autowired
    private RedisUtil redisUtil;

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        HttpServletResponse httpResponse = (HttpServletResponse) response;
        String requestURI = httpRequest.getRequestURI();
        /*String settlemonth =httpRequest.getParameter("settlemonth");
        if(settlemonth != null){
            httpRequest.setAttribute("settlemonth",settlemonth.replaceAll("-",""));
        }
*/
        Cookie[] cookies = httpRequest.getCookies();
        HttpSession session = httpRequest.getSession();
        // 链接来源地址
//        String referer = httpRequest.getHeader("referer");
//        log.info("requestURI:"+requestURI+"------"+"referer:"+referer);
//        if (StringUtils.isNotBlank(referer)) {
//            // 如果 链接地址来自其他网站，则返回错误页面
//           httpRequest.getRequestDispatcher("/reportJsp/error.jsp").forward(request, response);
//           return;
//        }
//
        if (requestURI.endsWith(".js") || requestURI.endsWith(".html")
                || requestURI.endsWith(".ico") || requestURI.endsWith(".css")
                || requestURI.endsWith(".png") || requestURI.endsWith(".jpg")
                || requestURI.endsWith(".gif")
                || requestURI.contains("error.jsp")
                || requestURI.contains("index.jsp")
                || requestURI.endsWith("/settle/reportAuditV3")
                || requestURI.endsWith("/settle/reportAudit")
                || requestURI.endsWith("/settle/SettleReport")
                || requestURI.endsWith("/settle/SettleReportV3")
                || requestURI.endsWith("/settle/reportAuditGetMenuTree")
                || requestURI.endsWith("/exportDownloadZip")
                || requestURI.endsWith("/report/isCheck")
                || requestURI.endsWith("/settle/getAllAcctMonth")
                || requestURI.endsWith("/isDownloadExcelZip")
                || requestURI.endsWith("/isDownloadPdfZip")
                || requestURI.endsWith("/reportServlet")
                || requestURI.endsWith("/exportRaq")
                || requestURI.endsWith("/reload")
                || requestURI.endsWith("/api/reportBatch")
                || requestURI.endsWith("/api/reportBatchDownload")
                || requestURI.endsWith("/api/reportDownload")
                || requestURI.endsWith("/api/evictReportBatch")
                || requestURI.endsWith("/templateDownload")
                || requestURI.endsWith("/RptOrigAll")
                || requestURI.endsWith("/api/prediction")
                || requestURI.endsWith("/api/predictionDownload")
                || requestURI.endsWith("/allProvUpload")
                || requestURI.endsWith("/erpService")
                || requestURI.endsWith("/common/isCheck")
                || requestURI.endsWith("/common/export")
                || requestURI.endsWith("/common/yunweiDownloadFiles")
                || requestURI.endsWith("/common/isDownloadZip")
                || requestURI.endsWith("/reportJsp/ReportBatch.jsp")
                || requestURI.endsWith("/reportJsp/Prediction.jsp")
                || requestURI.endsWith("/reportJsp/All_Prov_Upload.jsp")
                || requestURI.endsWith("/reportJsp/TemplateDownload.jsp")
                || requestURI.endsWith("/reportJsp/Rpt_Orig_all.jsp")
                || requestURI.endsWith("/report/verifyTicket")
                || requestURI.endsWith("/settleDetailReport/isDownloadExcel")
        ) {
            StringBuffer requestURL = httpRequest.getRequestURL();
            log.info("进入放行路径requestURL{}", requestURL);
            // 如果是静态资源，放行
            chain.doFilter(httpRequest, httpResponse);
        } else if (requestURI.endsWith("/rsa/getPublicKey")) {
            String authorization = httpRequest.getHeader("Authorization");
            String staffNum = httpRequest.getParameter("staffNum");
            String authKey = String.format(GoldBankConstants.STAFF_NUM_AUTH, staffNum);
            redisUtil.set(authKey,authorization);
            chain.doFilter(httpRequest, httpResponse);
        } else {
            //用session做缓存 判断是否第一次从showReport跳转 跳转后存入session用户名和公钥
//            String publicKey;
//            publicKey = (String) session.getAttribute("000admin");
//            log.info("session中的公钥publicKey:{}", publicKey);
//            if (StringUtils.isNotBlank(publicKey) &&  requestURI.endsWith("/queryReport.jsp")){
//                //判断是否和数据库中的公钥一样
//                String privateKey = reportCheckMapper.getPrivateKey(publicKey);
//                if (StringUtils.isNotBlank(privateKey)){
//                    httpRequest.getRequestDispatcher("/reportJsp/queryReport.jsp").forward(request, response);
//                    return;
//                }
//                //如果没查到 说明是伪造的公钥  且没有经过showReport.jsp这个步骤
//                httpRequest.getRequestDispatcher("/reportJsp/error.jsp").forward(request, response);
//                return;
//            }
//             //如果不是静态资源，进行其他解密页面路径的接口
//            log.info("需要解密的requestURI:::requestURI:{}", requestURI);
//            requestURI = requestURI.replace("//settle/", "");
//            log.info("需要解密的URI:::requestURI:{}", requestURI);
//            publicKey = httpRequest.getParameter("publicKey");
//            //解决加密过程中+丢失问题
//            publicKey = publicKey.replaceAll(" ", "+");
//            log.info("RSA公钥publicKey:{}", publicKey);
//            String privateKey = reportCheckMapper.getPrivateKey(publicKey);
//            log.info("RSA私钥privateKey:{}", privateKey);
//             //解密URL字符串
//            String decryptUrl = decryptUrlData(requestURI, privateKey);
//            log.info("RSA解密URL请求为:{}", decryptUrl);
//            if (decryptUrl.contains("showReport.jsp")){
//                session.setAttribute("000admin",publicKey);
//                //设置session过期时间
//                session.setMaxInactiveInterval(3600);
//            }
//            String contextPath = httpRequest.getContextPath();
//            String scheme = httpRequest.getScheme();
//            log.info("scheme:{}", scheme);
//            String serverName = httpRequest.getServerName();
//            int serverPort = httpRequest.getServerPort();
//            log.info("serverName:{}", serverName);
//            log.info("serverPort:{}", serverPort);
//            String basePath = httpRequest.getScheme()+"://"+httpRequest.getServerName()+":"+ httpRequest.getServerPort() + contextPath;
//            String basePath1 = httpRequest.getScheme()+"://"+httpRequest.getServerName()+ "/absweb/crmSettle/v3/reportJsp/showReport.jsp";
//            log.info("contextPath:{}", contextPath);
//            log.info("basePath:{}", basePath);
//            log.info("处理后用作转发basePath1:{}", basePath1);
//            httpRequest.getRequestDispatcher(decryptUrl).forward(request, response);
//            httpRequest.getRequestDispatcher(realPath).forward(request, response);
            if (requestURI.endsWith("/queryReport.jsp")){
                log.info("进入 /reportJsp/queryReport.jsp");
                httpRequest.getRequestDispatcher("/reportJsp/queryReport.jsp").forward(httpRequest, httpResponse);
            }
            if (requestURI.endsWith("/showReport.jsp")){
                httpRequest.getRequestDispatcher("/reportJsp/showReport.jsp").forward(httpRequest, httpResponse);
            }
        }
    }

    @Override
    public void destroy() {
    }

    private String decryptUrlData(String data, String privateKey) {
        // 返回解密后的URL
        String url = null;
        try {
            url = DataDecryptor.decryptData(data, privateKey);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return url;
    }
}
