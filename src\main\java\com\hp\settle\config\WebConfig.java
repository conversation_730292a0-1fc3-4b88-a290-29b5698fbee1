package com.hp.settle.config;

import org.apache.catalina.servlets.DefaultServlet;
import org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties;
import org.springframework.boot.web.server.WebServerFactoryCustomizer;
import org.springframework.boot.web.servlet.ServletRegistrationBean;
import org.springframework.boot.web.servlet.server.ConfigurableServletWebServerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.ViewResolver;
import org.springframework.web.servlet.config.annotation.*;
import org.springframework.web.servlet.view.InternalResourceViewResolver;
//import org.thymeleaf.spring5.SpringTemplateEngine;
//import org.thymeleaf.spring5.templateresolver.SpringResourceTemplateResolver;
//import org.thymeleaf.spring5.view.ThymeleafViewResolver;
//import org.thymeleaf.templateresolver.ITemplateResolver;

/**
 * Project: boss-service-settle
 * Date : 2023/10/16 12:09
 * Author : hw
 * Description : Web配置类
 */
//@Configuration
//@EnableWebMvc
//@ComponentScan
//public class WebConfig implements WebMvcConfigurer {
//
//
//    private static final String[] CLASSPATH_RESOURCE_LOCATIONS = {
//            "classpath:/META-INF/resources/",
//            "classpath:/resources/",
//            "classpath:/static/", "classpath:/static/node_modules/",
//            "classpath:/WEB-INF/"
//    };
//    private static final String[] CLASSPATH_RESOURCE_LOCATIONS = {"classpath:/static/" };

//    @Override
//    public void configureViewResolvers(ViewResolverRegistry registry) {
//        registry.jsp("/WEB-INF/views/", ".jsp"); // 配置 JSP 视图解析器
//    }
//
//    /**
//     * @Description: 注册jsp视图解析器
//     */
//    @Bean
//    public ViewResolver viewResolver() {
//        InternalResourceViewResolver resolver = new InternalResourceViewResolver();
//        resolver.setPrefix("/"); //配置放置jsp文件夹
//        resolver.setSuffix(".jsp");
//        resolver.setViewNames("jsp/*");  //重要 setViewNames 通过它识别为jsp页面引擎
//        resolver.setOrder(2);
//        return resolver;
//    }
//    /**
//     * @Description: 注册html视图解析器
//     */
//    @Bean
//    public ITemplateResolver templateResolver() {
//        SpringResourceTemplateResolver templateResolver = new SpringResourceTemplateResolver();
//        templateResolver.setTemplateMode("HTML");
//        templateResolver.setPrefix("classpath:/static/");
//        templateResolver.setSuffix(".html");
//        templateResolver.setCharacterEncoding("utf-8");
//        templateResolver.setCacheable(false);
//        return templateResolver;
//    }
//
//    /**
//     * @Description: 将自定义tml视图解析器添加到模板引擎并主持到ioc
//     */
//    @Bean
//    public SpringTemplateEngine templateEngine() {
//        SpringTemplateEngine templateEngine = new SpringTemplateEngine();
//        templateEngine.setTemplateResolver(templateResolver());
//        return templateEngine;
//    }
//    /**
//     * @Description: Thymeleaf视图解析器配置
//     */
//    @Bean
//    public ThymeleafViewResolver viewResolverThymeLeaf() {
//        ThymeleafViewResolver viewResolver = new ThymeleafViewResolver();
//        viewResolver.setTemplateEngine(templateEngine());
//        viewResolver.setCharacterEncoding("utf-8");
////        viewResolver.setViewNames(new String[]{"thymeleaf"});
//        viewResolver.setViewNames(null);
//        viewResolver.setOrder(1);
//        return viewResolver;
//    }
//
//    @Override
//    public void configureDefaultServletHandling(DefaultServletHandlerConfigurer configurer) {
//        configurer.enable();
//    }

//    @Bean
//    public ServletRegistrationBean defaultServlet(WebMvcProperties webMvcProperties) {
//        ServletRegistrationBean registrationBean = new ServletRegistrationBean();
//        registrationBean.setName("default");
//        registrationBean.setServlet(new DefaultServlet());
//        registrationBean.addUrlMappings(webMvcProperties.getServlet().getPath());
//        return registrationBean;
//    }
//    @Bean
//    WebServerFactoryCustomizer<ConfigurableServletWebServerFactory> enableDefaultServlet() {
//        return (factory) -> factory.setRegisterDefaultServlet(true);
//    }

    //registry.addResourceHandler("/img/**").addResourceLocations("/img/")
    // 表示将以 "/img/**" 开头的URL请求映射到 "/img/" 目录下的静态资源。这意味着当你访问 "/img/example.jpg" 时，它会尝试寻找 "/img/example.jpg" 文件并返回。

    //.addResourceHandler("/static/**").addResourceLocations("/WEB-INF/" + "/static/")
    // 表示将以 "/static/**" 开头的URL请求映射到 "/WEB-INF/static/" 目录下的静态资源。这意味着当你访问 "/static/css/style.css" 时，它会尝试寻找 "/WEB-INF/static/css/style.css" 文件并返回。
//    @Override
//    public void addResourceHandlers(ResourceHandlerRegistry registry) {
//        if (!registry.hasMappingForPattern("/webjars/**")) {
//            registry.addResourceHandler("/webjars/**").addResourceLocations(
//                    "classpath:/META-INF/resources/webjars/");
//        }
//        if (!registry.hasMappingForPattern("/**")) {
//            registry.addResourceHandler("/**").addResourceLocations(
//                    CLASSPATH_RESOURCE_LOCATIONS);
//        }
//
//    }

//    public void addResourceHandlers(ResourceHandlerRegistry registry) {
//        registry.addResourceHandler("/**").addResourceLocations("/WEB-INF/static/");
//    }
//}
