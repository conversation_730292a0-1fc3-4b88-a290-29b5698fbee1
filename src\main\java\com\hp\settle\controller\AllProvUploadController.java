package com.hp.settle.controller;

import com.hp.settle.service.AllProvUploadService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.nio.charset.StandardCharsets;

@Slf4j
@RestController
@RequestMapping("/allProvUpload")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AllProvUploadController {

    private final AllProvUploadService allProvUploadService;
    //省公司上传情况
    // http://localhost:9232/settleReport/allProvUpload
    // http://localhost:9232/settleReport/allProvUpload?R_KEY=10078&bssnum=yunwei&elemCode=settlementV7&settlemonth=202310
    @GetMapping
    public void getAllProvUpload(HttpServletRequest request, HttpServletResponse response) {
        log.info("***getAllProvUpload省公司账单上传情况start***");
        try {
            request.setCharacterEncoding(String.valueOf(StandardCharsets.UTF_8));
            response.setCharacterEncoding(String.valueOf(StandardCharsets.UTF_8));
            allProvUploadService.doFiles(request, response);
            log.info("***getAllProvUpload省公司账单上传情况end***");
        } catch (Exception e) {
            log.error("getAllProvUpload省公司账单上传情况error:{}", e.getMessage(), e);
        }
    }

    @PostMapping
    public void postAllProvUpload(HttpServletRequest request, HttpServletResponse response) {
        log.info("***postAllProvUpload省公司账单上传情况start***");
        try {
            request.setCharacterEncoding(String.valueOf(StandardCharsets.UTF_8));
            response.setCharacterEncoding(String.valueOf(StandardCharsets.UTF_8));
            allProvUploadService.doFiles(request, response);
            log.info("***postAllProvUpload省公司账单上传情况end***");
        } catch (Exception e) {
            log.error("postAllProvUpload省公司账单上传情况error:{}", e.getMessage(), e);
        }
    }

}
