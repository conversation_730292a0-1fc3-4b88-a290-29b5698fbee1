package com.hp.settle.controller;

import com.hp.settle.service.ReportGoldApproveService;
import com.hp.settle.vo.BaseRspsMsg;
import com.hp.settle.vo.GbTicketVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@RestController
@Slf4j
@RequestMapping("/report")
public class ReportGoldApproveController {

    @Autowired
    private ReportGoldApproveService reportGoldApproveService;

    @RequestMapping(value = "/isApprove", method = RequestMethod.GET)
    public String isApprove(HttpServletRequest request,
                            HttpServletResponse response){
        return reportGoldApproveService.isApprove(request, response);
    }

    @PostMapping("/verifyTicket")
    public BaseRspsMsg verifyTicket(@RequestBody GbTicketVo ticketVo) {
        return reportGoldApproveService.verifyTicket(ticketVo);
    }
}
