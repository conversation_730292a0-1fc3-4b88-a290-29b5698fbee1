package com.hp.settle.controller;

import com.hp.settle.service.RptOrigAllService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.nio.charset.StandardCharsets;

@Slf4j
@RestController
@RequestMapping("/RptOrigAll")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class RptOrigAllController {

    private final RptOrigAllService rptOrigAllService;
    //业绩还原
    // http://localhost:9232/settleReport/RptOrigAll?R_KEY=10078&bssnum=yunwei&elemCode=settlementV6&settlemonth=202310
    @GetMapping
    public void getRptOrigAll(HttpServletRequest request, HttpServletResponse response) {
        log.info("***getRptOrigAll业绩还原start***");
        try {
            request.setCharacterEncoding(String.valueOf(StandardCharsets.UTF_8));
            response.setCharacterEncoding(String.valueOf(StandardCharsets.UTF_8));
            rptOrigAllService.doFiles(request, response);
            log.info("***getRptOrigAll业绩还原end***");
        } catch (Exception e) {
            log.error("getRptOrigAll业绩还原error:{}", e.getMessage(), e);
        }
    }

    @PostMapping
    public void postAllProvUpload(HttpServletRequest request, HttpServletResponse response) {
        log.info("***postAllProvUpload省公司账单上传情况start***");
        try {
            request.setCharacterEncoding(String.valueOf(StandardCharsets.UTF_8));
            response.setCharacterEncoding(String.valueOf(StandardCharsets.UTF_8));
            rptOrigAllService.doFiles(request, response);
            log.info("***postAllProvUpload省公司账单上传情况end***");
        } catch (Exception e) {
            log.error("postAllProvUpload省公司账单上传情况error:{}", e.getMessage(), e);
        }
    }

}
