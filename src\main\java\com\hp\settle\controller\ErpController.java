package com.hp.settle.controller;

import com.hp.settle.api.R;
import com.hp.settle.service.ErpService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.nio.charset.StandardCharsets;

/**
 * Project: settle-service-report
 * Date : 2024/3/5 10:43
 * Author : hw
 * Description :
 */
@Slf4j
@RestController
@RequestMapping("/erpService")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ErpController {

    private final ErpService erpService;

    @GetMapping
    public R<String> getErpService(HttpServletRequest request, HttpServletResponse response) {
        log.info("***ERP文件生成start***");
        try {
            request.setCharacterEncoding(String.valueOf(StandardCharsets.UTF_8));
            response.setCharacterEncoding(String.valueOf(StandardCharsets.UTF_8));
            String fileName = erpService.doFiles(request, response);
            log.info("***ERP文件生成下载end***");
            return R.ok(fileName,1);
        } catch (Exception e) {
            log.error("ERP文件生成下载error:{}", e.getMessage(), e);
            return R.error(e.getMessage());
        }
    }

}