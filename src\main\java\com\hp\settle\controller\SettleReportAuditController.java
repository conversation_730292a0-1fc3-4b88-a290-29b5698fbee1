package com.hp.settle.controller;

import com.hp.settle.entity.ReportAudit;
import com.hp.settle.entity.SettleReportAudit;
import com.hp.settle.service.ReportAuditService;
import com.hp.settle.vo.BaseRspsMsg;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

@Controller
@Slf4j
@RequestMapping("/settle")
public class SettleReportAuditController {

    @Autowired
    private ReportAuditService reportAuditService;

    @RequestMapping(value = "/reportAuditV3", produces = "application/json;charset=UTF-8")
    public String reportAudit(HttpServletRequest request)
    {
            return "src/index";
    }

    /**
     * 功能描述:获取报表审核页面的报表树
     */
    @RequestMapping(value = "/reportAuditGetMenuTree", method = RequestMethod.GET, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public BaseRspsMsg getSettleReportAudit(@RequestParam("treeType") String treeType,
                                            @RequestParam("acctMonth") String acctMonth)  {

        BaseRspsMsg baseRspsMsg ;
        try{
            log.info("treeType",treeType);

            //JwtPayload jwtPayload = (JwtPayload)request.getAttribute(JwtPayload.class.getName());
            //全中心越权访问问题，前端传的staffnum和token获取的要一致，不一致则认为权限有问题
            //staffNum =  RSAUtils.decryptBase64ByPrivateKey(staffNum);
//            log.info("客户信息:{}",staffNum);
//            log.info("-->queryJobSpecByJobspecIdJobTypeId,jwtPayload:{}",jwtPayload==null?null: JSON.toJSONString(jwtPayload));
//            if (jwtPayload==null || StringUtils.isBlank(jwtPayload.getName()) || StringUtils.isBlank(staffNum) || !jwtPayload.getName().equals(staffNum)) {
//                baseRspsMsg = BaseRspsMsg.build(BaseRspsMsg.BIZ_CODE_00001_FAILE, "鉴权失败。");
//                return baseRspsMsg;
//            }
            baseRspsMsg = BaseRspsMsg.ok(reportAuditService.getReportAduditMenuTree(treeType,acctMonth));
        } catch (Exception e){
            log.error("系统异常！", e);
            baseRspsMsg = BaseRspsMsg.fail("系统异常");
        }
        return baseRspsMsg;
    }


    /*
    * 报表审核
    * */
    @RequestMapping(value = "/reportAudit", method = RequestMethod.POST, produces="application/json;charset=UTF-8")
    @ResponseBody
    //@BBossAuthen(required = true, requestParamName = "request", staffNumParamName = "settleReportAudit|$.staffNum")
    public BaseRspsMsg getReportAudit(@RequestBody SettleReportAudit settleReportAudit){
        BaseRspsMsg baseRspsMsg ;
        try{
            String staffNum= settleReportAudit.getStaffNum();
            log.info("客户信息staffNum:{}",staffNum);
            //JwtPayload jwtPayload = (JwtPayload)request.getAttribute(JwtPayload.class.getName());
            log.info("jwtPayload信息:{}",staffNum);
//            if(jwtPayload != null){
//                log.info("jwtPayload的name信息:{}",jwtPayload.getName());
//            }
            //全中心越权访问问题，前端传的staffnum和token获取的要一致，不一致则认为权限有问题
            //staffNum =  RSAUtils.decryptBase64ByPrivateKey(staffNum);
            //settleReportAudit.setStaffNum(staffNum);
            //log.info("-->queryJobSpecByJobspecIdJobTypeId,jwtPayload:{}",jwtPayload==null?null: JSON.toJSONString(jwtPayload));
            //if (jwtPayload==null || StringUtils.isBlank(jwtPayload.getName()) || StringUtils.isBlank(staffNum) || flag || !jwtPayload.getName().equals(staffNum)) {
            //    baseRspsMsg = BaseRspsMsg.build(BaseRspsMsg.BIZ_CODE_00001_FAILE, "鉴权失败。");
            //    return baseRspsMsg;
            //}
            List<ReportAudit> reportAuditList = settleReportAudit.getReportAudit();
            baseRspsMsg = BaseRspsMsg.ok(reportAuditService.reportAudit(reportAuditList,staffNum));
            //baseRspsMsg = BaseRspsMsg.build("00000","审核通过", reportAuditService.reportAudit(reportAuditList));
        } catch (Exception e){
            log.error("系统异常！", e);
            baseRspsMsg = BaseRspsMsg.fail("系统异常");
        }
        return baseRspsMsg;
    }


}
