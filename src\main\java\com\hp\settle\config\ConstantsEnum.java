package com.hp.settle.config;

/**
 * 报表对应表KEY及下载文件名称及打包名称
 */
public enum ConstantsEnum {
    /**
     * TAXRATE : 税率
     * ENTITY  : 签约主体
     * YYYYMM  : 结算账期
     * INCOME  :
     * INOUT   : 结入结出
     */
    STL_10001("10001", "BL", "应收收入(TAXRATE%税率)_ENTITYADJUSTYYYYMM", 					"应收收入总表ADJUST"), //OK
    STL_10002("10002", "AR", "实收收入(TAXRATE%税率)_ENTITYADJUSTYYYYMM", 					"实收收入总表ADJUST"),//OK
    STL_10007("10007", "BL", "D202_政企分公司主办集团客户业务应收结算单(TAXRATE%)ADJUSTYYYYMM", 		"政企分公司主办集团客户业务结算单应收(总表)ADJUST"),//OK
    STL_10008("10008", "AR", "D202_政企分公司主办集团客户业务实收结算单(TAXRATE%)ADJUSTYYYYMM", 		"政企分公司主办集团客户业务结算单实收(总表)ADJUST"),//OK
    STL_10009("10009", "BL", "D202_应收结算单ADJUST(TAXRATE%)_YYYYMM_特殊分表", 									"政企分公司主办集团客户业务结算单应收(分省)ADJUST"),//OK
    STL_10010("10010", "AR", "D202_实收结算单ADJUST(TAXRATE%)_YYYYMM_特殊分表", 									"政企分公司主办集团客户业务结算单实收(分省)ADJUST"),//OK
    STL_10013("10013", "BL", "政企自有产品应收结算单ADJUST(TAXRATE%税率)_ENTITY_YYYYMM", 				"政企自有产品应收结算单ADJUST"),//OK
    STL_10014("10014", "AR", "政企自有产品实收结算单ADJUST(TAXRATE%税率)_ENTITY_YYYYMM", 				"政企自有产品实收结算单ADJUST"),//OK
    STL_10015("10015", "BL", "政企公司销售支撑费金额分省结算单ADJUST_YYYYMM", 					"销售支撑收入分摊ADJUST"),//OK
    STL_10019("10019", "BL", "D208_省间应收结算单ADJUST_YYYYMM_特殊分表", 									"省公司主办集团客户业务分省明细结算应收ADJUST"),//OK
    STL_10020("10020", "AR", "D208_省间实收结算单ADJUST_YYYYMM_特殊分表", 									"省公司主办集团客户业务分省明细结算实收ADJUST"),//OK
    STL_10021("10021", "BL", "D208_省公司主办集团客户业务应收结算单(TAXRATE%)ADJUSTYYYYMM", 			"省公司主办集团客户业务结算单应收ADJUST"),//OK
    STL_10022("10022", "AR", "D208_省公司主办集团客户业务实收结算单ADJUST(TAXRATE%)_YYYYMM", 			"省公司主办集团客户业务结算单实收ADJUST"),//OK
    STL_10023("10023", "BL", "应收欠费账龄表_YYYYMM", 									"应收欠费账龄表"),  //OK
    STL_10024("10024", "BL", "运营支撑收入分省汇总结算单ADJUST_YYYYMM", 						"运营支撑费分摊ADJUST"),//OK
    STL_10034("10034", "AR", "D232_中国移动车务通业务结算单ADJUST_YYYYMM", 							"车务通业务结算单ADJUST"),//OK
    STL_10146("10146", "AR", "D232_中国移动车务通业务结算单ADJUST_YYYYMM_erp", 							"车务通业务结算单ADJUST_erp"),//OK
    STL_10035("10035", "AR", "各省公司结算收入发票开具明细(TAXRATE%税率)_INOUT_YYYYMM", 	"BBOSS发票开具明细表"),//OK
    STL_10036("10036", "BL", "政务易统计报表ADJUST_YYYYMM", 									"政务易统计报表ADJUST"),//OK
    STL_10037("10037", "BL", "中国移动车务通业务运营支撑费虚拟结算单ADJUST_YYYYMM", 				"车务通虚拟收入结算单ADJUST"),//OK
    STL_10038("10038", "BL", "车务通业务收入统计应收报表ADJUST_YYYYMM", 						"车务通业务收入统计应收报表ADJUST"),//OK
    STL_10039("10039", "AR", "车务通业务收入统计实收报表ADJUST_YYYYMM", 						"车务通业务收入统计实收报表ADJUST"),//OK
    STL_10040("10040", "BL", "流量统付全网业务分客户结算明细表应收_YYYYMM", 				"流量统付全网业务分客户结算明细表应收"),//OK


    STL_10003("10003", "BL", "应收结算单（总表）ADJUST-INCOME", 			""),//OK
    STL_10004("10004", "AR", "实收结算单（总表）ADJUST", 					""),//OK
    STL_10005("10005", "BL", "应收结算单（分省）ADJUST", 					""),//OK
    STL_10006("10006", "AR", "实收结算单（分省）ADJUST", 					""),//OK
    STL_10011("10011", "BL", "总部一点支付客户应收收入统计表（分EC）ADJUST", 	""),//OK
    STL_10012("10012", "AR", "总部一点支付客户实收收入统计表（分EC）ADJUST", 	""),//OK
    STL_10016("10016", "BL", "总部受理数据专线业务结算单ADJUST", 			""),//OK
    STL_10017("10017", "BL", "主办省一点支付客户应收结算单ADJUST",		 	""),//OK
    STL_10018("10018", "AR", "主办省一点支付客户实收结算单ADJUST", 			""),//OK
    STL_10025("10025", "BL", "免资费流量统计报表", 					""),//OK
    STL_10027("10027", "BL", "数据专线结算单(总表)ADJUST", 				"数据专线结算单(总表)ADJUST"),//OK
    STL_10028("10028", "BL", "数据专线结算单(分省)ADJUST", 				"数据专线结算单(分省)ADJUST"),//OK
    STL_10029("10029", "BL", "数据专线业务结算客户数ADJUST", 				""),//OK
    STL_10030("10030", "BL", "收入流量统计报表", 					""),//OK
    STL_10031("10031", "BL", "总部客户欠费报表", 					""),//OK
    STL_10032("10032", "BL", "总部一点支付客户收入报表ADJUST", 				""),//OK
    STL_10033("10033", "BL", "客户经理签单统计表", 					""),//OK
    STL_10041("10041", "BL", "酬金结算", 							"酬金结算"),//OK
    STL_10042("10042", "BL", "考核酬金", 							""),//OK
    STL_10043("10043", "BL", "销售支撑协同报表(总表)", 				""),//OK
    STL_10044("10044", "BL", "呼叫中心直连", 						""),//OK
    STL_10045("10045", "BL", "跨省互联网专线", 						""),//OK
    STL_10046("10046", "BL", "跨省数据专线", 						""),//OK
    STL_10047("10047", "BL", "ERP", 						""),//OK
    STL_10048("10048", "BL", "BAM", 						""),//OK
    STL_10049("10049", "BL", "BAM", 						""),//OK
    STL_10050("10050", "BL", "BAM", 						""),//OK
    STL_10051("10051", "BL", "BAM", 						""),//OK
    STL_10052("10052", "BL", "BAM", 						""),//OK
    STL_10053("10053", "BL", "政企签约客户应收结出总表",             ""),

    STL_10054("10054", "BL", "结算报表稽核查询表",                ""),

    STL_10055("10055", "BL", "YYYYMM_云mas_汇总",                   "云mas_汇总"),
    STL_10056("10056", "BL", "YYYYMM_云mas_已上传",                   "云mas_已上传"),
    STL_10057("10057", "BL", "YYYYMM_云mas_应上传",             "云mas_应上传"),
    STL_10058("10058", "BL", "YYYYMM_云mas_应上传但未上传",             "云mas_应上传但未上传"),
    STL_10059("10059", "BL", "YYYYMM_云mas_不要求上传但上传",               "云mas_不要求上传但上传"),

    STL_10060("10060", "BL", "YYYYMM_和对讲_不要求上传但上传",             "和对讲_不要求上传但上传"),
    STL_10061("10061", "BL", "YYYYMM_和对讲_汇总",                    "和对讲_汇总"),
    STL_10062("10062", "BL", "YYYYMM_和对讲_已上传",                   "和对讲_已上传"),
    STL_10063("10063", "BL", "YYYYMM_和对讲_未上传",                   "和对讲_未上传"),
    STL_10064("10064", "BL", "YYYYMM_和对讲_应上传",                   "和对讲_应上传"),

    STL_10065("10065", "BL", "YYYYMM_一点支付_省公司上传情况",             ""),
    STL_10066("10066", "BL", "YYYYMM_一点支付_有问题号码",                ""),

    STL_10067("10067", "BL", "YYYYMM_流量统付_各省明细",                 ""),
    STL_10068("10068", "BL", "YYYYMM_流量统付_各省汇总",                 ""),
    STL_10069("10069", "BL", "农政通ADJUST_YYYYMM",                       "农政通"),
    STL_10078("10078", "BL", "YYYYMM_M2M_汇总",                       ""),
    STL_10079("10079", "BL", "YYYYMM_呼叫中心直联_汇总",                       ""),
    STL_10080("10080", "BL", "YYYYMM_跨省产品一点支付_汇总",                       ""),

    STL_10070("10070", "BL", "集团产品结算收入明细表",             "集团产品结算收入明细表"),
    STL_10071("10071", "BL", "用户欠费分析表_YYYYMM",             ""),
    STL_10072("10072", "BL", "国际公司专线结算单_YYYYMM",             "国际公司专线结算单"),
    STL_10073("10073", "BL", "省间应收分省汇总结算单_有限政企分公司ADJUST_YYYYMM",             ""),
    STL_10074("10074", "BL", "政企行业产品集团客户业务分省应收结算单YYYYMM(TAXRATE%)_特殊分表",             "政企行业产品集团客户业务分省应收结算单"),
    STL_10075("10075", "BL", "政企行业产品集团客户业务应收结算单(TAXRATE%)_YYYYMM",             "政企行业产品集团客户业务应收结算单"),
    STL_10076("10076", "AR", "政企分公司主办集团客户业务资金结算单_YYYYMM",             ""),
    STL_10077("10077", "BL", "战略客户销售支撑费清单_YYYYMM",             ""),
    STL_10081("10081", "BL", "云MAS运营支撑费明细表_YYYYMM",                       "云MAS运营支撑费明细表"),

    STL_10090("10090", "BL", "政企行业产品集团客户业务分省应收结算单YYYYMM(TAXRATE%)_特殊分表",             "政企行业产品集团客户业务分省应收结算单"),
    STL_10091("10091", "BL", "政企行业产品集团客户业务应收结算单(TAXRATE%)_YYYYMM",             "政企行业产品集团客户业务应收结算单"),

    STL_10092("10092", "BL", "双跨专线结算补充报表（MPLS VPN专线）ADJUST-总表", "双跨专线结算补充报表（MPLS VPN专线）ADJUST-总表"),
    STL_10093("10093", "BL", "双跨专线结算补充报表（跨省互联网专线）ADJUST-总表", "双跨专线结算补充报表（跨省互联网专线）ADJUST-总表"),
    STL_10094("10094", "BL", "双跨专线结算补充报表（跨省、专网、跨境专线）ADJUST-总表", "双跨专线结算补充报表（跨省、专网、跨境专线）ADJUST-总表"),
    STL_10095("10095", "BL", "双跨专线结算补充报表（MPLS VPN专线）ADJUST-发起方分表", "双跨专线结算补充报表（MPLS VPN专线）ADJUST-发起方分表"),
    STL_10096("10096", "BL", "双跨专线结算补充报表（跨省互联网专线）ADJUST-发起方分表", "双跨专线结算补充报表（跨省互联网专线）ADJUST-发起方分表"),
    STL_10097("10097", "BL", "双跨专线结算补充报表（跨省、专网、跨境专线）ADJUST-发起方分表", "双跨专线结算补充报表（跨省、专网、跨境专线）ADJUST-发起方分表"),
    STL_10098("10098", "BL", "双跨专线结算补充报表（MPLS VPN专线）ADJUST-落地方分表", "双跨专线结算补充报表（MPLS VPN专线）ADJUST-落地方分表"),
    STL_10099("10099", "BL", "双跨专线结算补充报表（跨省互联网专线）ADJUST-落地方分表", "双跨专线结算补充报表（跨省互联网专线）ADJUST-落地方分表"),
    STL_10100("10100", "BL", "双跨专线结算补充报表（跨省、专网、跨境专线）ADJUST-落地方分表", "双跨专线结算补充报表（跨省、专网、跨境专线）ADJUST-落地方分表"),
    STL_10101("10101", "BL", "D313-1_政企条线云能力中心产品支撑费结算单(TAXRATE%)_YYYYMM", "D313-1_政企条线云能力中心产品支撑费结算单ADJALL_YYYYMM"),
    STL_10102("10102", "BL", "D332_集团客户业务代收代付-云能力中心(TAXRATE%)_YYYYMM", "D332_集团客户业务代收代付-云能力中心ADJALL_YYYYMM"),
    STL_10103("10103", "BL", "移动云合作伙伴结算单_YYYYMM", "移动云合作伙伴结算单_YYYYMM"),
    STL_10104("10104", "BL", "华为合营云合作伙伴结算报表_YYYYMM", "华为合营云合作伙伴结算报表_YYYYMM"),
    STL_10105("10105", "BL", "公播音乐合作伙伴结算单", "公播音乐合作伙伴结算单"),
    STL_10106("10106", "BL", "D313-3_政企条线上研院产品支撑费结算单(TAXRATE%)_YYYYMM", "D313-3_政企条线上研院产品支撑费结算单ADJALL_YYYYMM"),
    STL_10107("10107", "BL", "D313-2_政企条线成研院产品支撑费结算单(TAXRATE%)_YYYYMM", "D313-2_政企条线成研院产品支撑费结算单ADJALL_YYYYMM"),
    STL_10108("10108", "BL", "D335-2-政企合作分成业务-成研_YYYYMM", "D335-2-政企合作分成业务-成研ADJALL_YYYYMM"),
    STL_10109("10109", "BL", "D313-4_政企条线雄研院产品支撑费结算单(TAXRATE%)_YYYYMM", "D313-4_政企条线雄研院产品支撑费结算单ADJALL_YYYYMM"),
    STL_10110("10110", "BL", "D313_政企条线政企事业部产品支撑费结算单(TAXRATE%)_YYYYMM", "D313_政企条线政企事业部产品支撑费结算单ADJALL_YYYYMM"),
    STL_10111("10111", "BL", "D313-7_政企条线咪咕公司产品支撑费结算单(TAXRATE%)_YYYYMM", "D313-7_政企条线咪咕公司产品支撑费结算单ADJALL_YYYYMM"),
    STL_10112("10112", "BL", "D313-8_政企条线互联网公司产品支撑费结算单(TAXRATE%)_YYYYMM", "D313-8_政企条线互联网公司产品支撑费结算单ADJALL_YYYYMM"),
    STL_10113("10113", "BL", "政企事业部与省公司DICT增值服务产品结算单(TAXRATE%)", "政企事业部与省公司DICT增值服务产品结算单ADJALL"),
    STL_10114("10114", "BL", "政企事业部与合作伙伴DICT增值服务产品结算单(TAXRATE%)_YYYYMM", "政企事业部与合作伙伴DICT增值服务产品结算单ADJALL_YYYYMM"),
    STL_10115("10115", "BL", "阿里云转售业务结算单_YYYYMM", "阿里云转售业务结算单_YYYYMM"),
    STL_10116("10116", "BL", "D335_政企条线政企事业部产品合作分成结算单(TAXRATE%)_YYYYMM", "D335_政企条线政企事业部产品合作分成结算单ADJALL_YYYYMM"),
    STL_10117("10117", "BL", "公播音乐合作伙伴产品业务提供商结算收入报表_YYYYMM", "公播音乐合作伙伴产品业务提供商结算收入报表"),
    STL_10118("10118", "BL", "和对讲合作伙伴产品业务提供商结算收入报表_YYYYMM", "和对讲合作伙伴产品业务提供商结算收入报表"),
    STL_10119("10119", "BL", "千里眼合作伙伴产品业务提供商结算收入报表_YYYYMM", "千里眼合作伙伴产品业务提供商结算收入报表"),
    STL_10120("10120", "BL", "D340-政企捆绑销售业务结算单-咪咕_YYYYMM", "D340-政企捆绑销售业务结算单-咪咕_YYYYMM"),
    STL_10121("10121", "BL", "移动办公-联想权益合作伙伴产品业务提供商结算收入报表_YYYYMM", "移动办公-联想权益合作伙伴产品业务提供商结算收入报表"),
    STL_10122("10122", "BL", "移动办公-企业应用合作伙伴产品业务提供商结算收入报表_YYYYMM", "移动办公-企业应用合作伙伴产品业务提供商结算收入报表"),
    STL_10123("10123", "BL", "D335-5_政企条线互联网公司产品合作分成结算单(TAXRATE%)_YYYYMM", "D335-5_政企条线互联网公司产品合作分成结算单_YYYYMM"),
    STL_10124("10124", "BL", "省专结算报表-位置基地_YYYYMM", "省专结算报表-位置基地"),
    STL_10125("10125", "BL", "移动办公-i笛云合作伙伴产品业务提供商结算收入报表_YYYYMM", "移动办公-i笛云合作伙伴产品业务提供商结算收入报表_YYYYMM"),
    STL_10126("10126", "BL", "移动办公-口袋扫描仪合作伙伴产品业务提供商结算收入报表_YYYYMM", "移动办公-口袋扫描仪合作伙伴产品业务提供商结算收入报表_YYYYMM"),
    STL_10127("10127", "BL", "移动办公-公文管理、督办管理、发票识别验真合作伙伴产品业务提供商结算收入报表_YYYYMM", "移动办公-公文管理、督办管理、发票识别验真合作伙伴产品业务提供商结算收入报表_YYYYMM"),
    STL_10128("10128", "BL", "D313-10_政企条线卓望公司产品支撑费结算单(TAXRATE%)_YYYYMM", "D313-10_政企条线卓望公司产品支撑费结算单ADJALL_YYYYMM"),
    STL_10129("10129", "BL", "D313-6_政企条线集成公司产品支撑费结算单(TAXRATE%)_YYYYMM", "D313-6_政企条线集成公司产品支撑费结算单ADJALL_YYYYMM"),
    STL_10130("10130", "BL", "移动办公-WPS合作伙伴产品业务提供商结算收入报表_YYYYMM", "移动办公-WPS合作伙伴产品业务提供商结算收入报表_YYYYMM"),
    STL_10131("10131", "BL", "D335-1_政企条线云能力中心产品合作分成结算单(TAXRATE%)_YYYYMM", "D335-1_政企条线云能力中心产品合作分成结算单ADJALL_YYYYMM"),
    STL_10132("10132", "BL", "移动办公-福昕权益合作伙伴产品业务提供商结算收入报表_YYYYMM", "移动办公-福昕权益合作伙伴产品业务提供商结算收入报表_YYYYMM"),
    STL_10133("10133", "BL", "移动办公-有道云笔记合作伙伴产品业务提供商结算收入报表_YYYYMM", "移动办公-有道云笔记合作伙伴产品业务提供商结算收入报表_YYYYMM"),
    STL_10134("10134", "BL", "移动办公-ProcessOn合作伙伴产品业务提供商结算收入报表_YYYYMM", "移动办公-ProcessOn合作伙伴产品业务提供商结算收入报表_YYYYMM"),
    STL_10135("10135", "BL", "移动办公-Microsoft 365合作伙伴产品业务提供商结算收入报表_YYYYMM", "移动办公-Microsoft 365合作伙伴产品业务提供商结算收入报表_YYYYMM"),
    STL_10136("10136", "BL", "D335-6_政企条线终端公司产品结算单(TAXRATE%)_YYYYMM", "D335-6_政企条线终端公司产品结算单ADJALL_YYYYMM"),
    STL_10137("10137", "BL", "D313-7-咪咕公司支撑费明细-企业彩印名片彩印-能力开放", "D313-7-咪咕公司支撑费明细-企业彩印名片彩印-能力开放"),
    STL_10138("10138", "BL", "D313-7-咪咕公司支撑费明细-企业彩印热线彩印-能力开放", "D313-7-咪咕公司支撑费明细-企业彩印热线彩印-能力开放"),
    STL_10139("10139", "BL", "BBOSS应收收入汇总表_YYYYMM", "BBOSS应收收入汇总表"),
    STL_10140("10140", "AR", "BBOSS实收收入汇总表_YYYYMM", "BBOSS实收收入汇总表"),
    STL_10141("10141", "BL", "BBOSS应收收入汇总表_YEAR", "BBOSS应收收入汇总表_历史"),
    STL_10142("10142", "AR", "BBOSS实收收入汇总表_YEAR", "BBOSS实收收入汇总表_历史"),
    STL_10143("10143", "BL", "D313-10-卓望公司支撑费明细-专线卫士-能力开放_YYYYMM", "D313-10-卓望公司支撑费明细-专线卫士-能力开放_YYYYMM"),
    STL_10144("10144", "BL", "中国移动CDN合作伙伴结算单_YYYYMM", "中国移动CDN合作伙伴结算单ADJALL_YYYYMM"),
    STL_10145("10145", "BL", "D313-11_政企条线金科公司产品支撑费结算单(TAXRATE%)_YYYYMM", "D313-11_政企条线金科公司产品支撑费结算单ADJALL_YYYYMM"),
    STL_10147("10147", "BL", "D313-12_政企条线物联网公司产品支撑费结算单(TAXRATE%)_YYYYMM", "D313-12_政企条线物联网公司产品支撑费结算单ADJALL_YYYYMM"),
    STL_10148("10148", "BL", "中移物联网公司与合作伙伴DICT增值服务产品结算单_YYYYMM", "中移物联网公司与合作伙伴DICT增值服务产品结算单_YYYYMM"),
    STL_10149("10149", "BL", "D313-13_政企条线设计院产品支撑费结算单(TAXRATE%)_YYYYMM", "D313-13_政企条线设计院产品支撑费结算单ADJALL_YYYYMM"),
    STL_10150("10150", "BL", "中移物联网公司OnePark与合作伙伴DICT增值服务产品结算单_YYYYMM", "中移物联网公司OnePark与合作伙伴DICT增值服务产品结算单_YYYYMM"),
    // 20230724 start
    STL_10151("10151", "BL", "D313-12_省专结算报表-千里眼(TAXRATE%)_YYYYMM-分表", "D313-12_省专结算报表-千里眼_YYYYMM"),
    STL_10152("10152", "BL", "D313-12_省专结算报表-OneNET(TAXRATE%)_YYYYMM-分表", "D313-12_省专结算报表-OneNET_YYYYMM"),
    STL_10153("10153", "BL", "D313-12_省专结算报表-OnePark(TAXRATE%)_YYYYMM-分表", "D313-12_省专结算报表-OnePark_YYYYMM"),
    STL_10154("10154", "BL", "中移物联网公司与合作伙伴OneNET DICT增值服务产品结算单(TAXRATE%)_YYYYMM", "中移物联网公司与合作伙伴OneNET DICT增值服务产品结算单ADJALL_YYYYMM"),
    STL_10155("10155", "BL", "中移物联网公司与合作伙伴OnePark DICT增值服务产品结算单(TAXRATE%)_YYYYMM", "中移物联网公司与合作伙伴OnePark DICT增值服务产品结算单ADJALL_YYYYMM"),
    // 20230724 end
    // 20230724 start
    STL_10156("10156", "BL", "D313-5_自有移动云产品支撑费结算单-云能力中心(TAXRATE%)_YYYYMM", "D313-5_自有移动云产品支撑费结算单-云能力中心ADJALL_YYYYMM"),
    // 20230724 end
    STL_10157("10157", "BL", "物联网公司与行车卫士合作伙伴DICT集成服务产品结算单(TAXRATE%)_YYYYMM", "物联网公司与行车卫士合作伙伴DICT集成服务产品结算单ADJALL_YYYYMM"),
    // 20231009 start
    STL_10158("10158", "BL", "中国移动CDN合作伙伴分省结算单(PROV)_YYYYMM", "中国移动CDN合作伙伴分省结算单ADJALL_YYYYMM"),
    // 20231009 end
    STL_10159("10159", "BL", "D313-8-省专结算报表-阅信业务分表(TAXRATE%)_YYYYMM", "D313-8-省专结算报表-阅信业务分表ADJALL_YYYYMM"),
    //智慧流程基础服务包合作伙伴报表
    STL_10160("10160", "BL", "移动办公-智慧流程基础服务包业务提供商结算收入报表_YYYYMM", "移动办公-智慧流程基础服务包业务提供商结算收入报表_YYYYMM"),
    STL_10161("10161", "BL", "D208-1_省公司主办集团客户业务结算单（IDC协同营销）ADJUST(TAXRATE%)_YYYYMM", "D208-1_省公司主办集团客户业务结算单（IDC协同营销）ADJUST"),
    STL_10163("10163", "BL", "D313_政企条线专业公司标准产品DICT服务包结算单(TAXRATE%)_YYYYMM", "政企条线专业公司标准产品DICT服务包结算单_D313ADJALL_YYYYMM"),
    STL_10164("10164", "BL", "D313_政企条线专业公司标准产品DICT服务包结算单(TAXRATE%)_YYYYMM_分表", "政企条线专业公司标准产品DICT服务包结算单_D313ADJALL_YYYYMM_分表"),
    STL_10165("10165", "BL", "D313-12_省专结算报表-行车卫士(TAXRATE%)_YYYYMM-分表", "D313-12_省专结算报表-行车卫士_YYYYMM"),
    STL_10166("10166", "BL", "移动办公-数字人名片产品业务提供商结算收入报表_YYYYMM", "移动办公-数字人名片产品业务提供商结算收入报表_YYYYMM"),
    STL_10167("10167", "BL", "移动云联创产品合作伙伴结算单_YYYYMM", "移动云联创产品合作伙伴结算单_YYYYMM"),
    STL_10168("10168", "BL", "D208-1_省公司主办集团客户业务结算单（IDC协同营销）ADJUST_YYYYMM_特殊分表", "D208-1_省公司主办集团客户业务结算单（IDC协同营销）ADJUST_YYYYMM_特殊分表"),
    //20240401 end
    STL_10169("10169", "BL", "省公司与合作伙伴DICT增值服务包结算单(TAXRATE%)_YYYYMM_特殊分表", "省公司与合作伙伴DICT增值服务包结算单_YYYYMM"),
    STL_10170("10170", "BL", "大视频产品部和对讲DICT增值服务结算单(TAXRATE%)_YYYYMM", "大视频产品部和对讲DICT增值服务结算单_YYYYMM"),
    STL_10171("10171", "BL", "大视频产品部云视讯DICT增值服务结算单(TAXRATE%)_YYYYMM", "大视频产品部云视讯DICT增值服务结算单_YYYYMM"),
    STL_10172("10172", "BL", "和商务TV合作伙伴服务包结算单(TAXRATE%)_YYYYMM", "和商务TV合作伙伴服务包结算单(TAXRATE%)_YYYYMM"),
    STL_10173("10173", "BL", "e企组网合作伙伴服务包结算单(TAXRATE%)_YYYYMM", "e企组网合作伙伴服务包结算单(TAXRATE%)_YYYYMM"),
    STL_10174("10174", "BL", "移动办公合作伙伴服务包结算单(TAXRATE%)_YYYYMM", "移动办公合作伙伴服务包结算单(TAXRATE%)_YYYYMM"),

    STL_10175("10175", "BL", "S201_中国移动带宽型业务省间结算单_YYYYMM", "S201_中国移动带宽型业务省间结算单_YYYYMM"),
    STL_10176("10176", "BL", "S201_中国移动带宽型业务省间结算单_YYYYMM_特殊分表", "S201_中国移动带宽型业务省间结算单_YYYYMM_特殊分表"),
    STL_10178("10178", "BL", "V101_省公司主办集团客户业务结算单（省间直接结算）(TAXRATE%)ADJUSTYYYYMM", "V101_省公司主办集团客户业务结算单（省间直接结算）_YYYYMM"),
    STL_10179("10179", "BL", "V101_省公司主办集团客户业务结算单（省间直接结算）ADJUST_YYYYMM_特殊分表", "V101_省公司主办集团客户业务结算单（省间直接结算）_YYYYMM_特殊分表"),
    STL_10180("10180", "BL", "D313-9_政企条线中移智行产品支撑费结算单(TAXRATE%)_YYYYMM", "D313-9_政企条线中移智行产品支撑费结算单_YYYYMM"),
    STL_10181("10181", "BL", "D313-10-卓望公司支撑费明细-专线卫士安全增值服务_YYYYMM", "D313-10-卓望公司支撑费明细-专线卫士安全增值服务_YYYYMM"),
    STL_10182("10182", "BL", "移动办公-数智旅行产品业务提供商结算收入报表ADJALL2_YYYYMM", "移动办公-数智旅行产品业务提供商结算收入报表ADJALL_YYYYMM"),
    STL_10183("10183", "BL", "OneS星辰安全产品结算单ADJALL2_YYYYMM", "OneS星辰安全产品结算单ADJALL_YYYYMM"),
    STL_10184("10184", "BL", "OneS星辰安全产品结算单ADJALL2_YYYYMM_分表_PROV", "OneS星辰安全产品结算单ADJALL_YYYYMM_分表"),
    STL_10185("10185", "BL", "优享版专线安全检测服务结算单ADJALL2_YYYYMM", "优享版专线安全检测服务结算单ADJALL_YYYYMM"),
    STL_10186("10186", "BL", "优享版专线安全检测服务结算单ADJALL2_YYYYMM_分表_PROV", "优享版专线安全检测服务结算单ADJALL_YYYYMM_分表"),
    STL_10187("10187", "BL", "D313-5_自有移动云产品支撑费结算单-云能力中心(TAXRATE%)_YYYYMM_分表", "D313-5_自有移动云产品支撑费结算单-云能力中心_YYYYMM_分表"),

    STL_10188("10188", "BL", "OnePay合作伙伴服务包结算单(TAXRATE%)_YYYYMM", "OnePay合作伙伴服务包结算单(TAXRATE%)_YYYYMM"),
    STL_10189("10189", "BL", "e企赢客合作伙伴服务包结算单(TAXRATE%)_YYYYMM", "e企赢客合作伙伴服务包结算单(TAXRATE%)_YYYYMM"),
    STL_10190("10190", "BL", "5G消息合作伙伴服务包结算单(TAXRATE%)_YYYYMM", "5G消息合作伙伴服务包结算单(TAXRATE%)_YYYYMM"),
    STL_10191("10191", "BL", "专线卫士合作伙伴服务包结算单(TAXRATE%)_YYYYMM", "专线卫士合作伙伴服务包结算单(TAXRATE%)_YYYYMM"),
    STL_10192("10192", "BL", "5G消息终端结算明细数据_YYYYMM", "5G消息终端结算明细数据_YYYYMM"),

    STL_10193("10193", "BL", "大视频产品部和商务直播DICT增值服务结算单(TAXRATE%)_YYYYMM", "大视频产品部和商务直播DICT增值服务结算单_YYYYMM"),

    STL_10194("10194", "BL", "和对讲DICT增值服务产品结算单(TAXRATE%)_YYYYMM_特殊分表", "和对讲DICT增值服务产品结算单_YYYYMM"),
    STL_10195("10195", "BL", "云视讯DICT增值服务产品结算单(TAXRATE%)_YYYYMM_特殊分表", "云视讯DICT增值服务产品结算单_YYYYMM"),
    STL_10196("10196", "BL", "和商务直播DICT增值服务产品结算单(TAXRATE%)_YYYYMM_特殊分表", "和商务直播DICT增值服务产品结算单_YYYYMM"),

    STL_10197("10197", "BL", "D335-7_政企条线呼池智算产品合作分成结算单(TAXRATE%)_YYYYMM", "D335-7_政企条线呼池智算产品合作分成结算单_YYYYMM"),
    STL_10198("10198", "BL", "D335-7_政企条线呼池智算产品合作分成结算单(TAXRATE%)_YYYYMM_分表", "D335-7_政企条线呼池智算产品合作分成结算单_YYYYMM_分表"),
    STL_10199("10199", "BL", "Z003_政企条线呼池智算产品支撑费结算单(TAXRATE%)_YYYYMM", "Z003_政企条线呼池智算产品支撑费结算单_YYYYMM"),
    STL_10200("10200", "BL", "Z003_政企条线呼池智算产品支撑费结算单(TAXRATE%)_YYYYMM_分表", "Z003_政企条线呼池智算产品支撑费结算单_YYYYMM_分表"),

    STL_10201("10201", "BL", "D313-14_政企条线信息技术公司产品支撑费结算单(TAXRATE%)_YYYYMM", "D313-14_政企条线信息技术公司产品支撑费结算单_YYYYMM"),
    STL_10202("10202", "BL", "D313-5_自有移动云产品支撑费结算单-云能力中心(TAXRATE%)_YYYYMM_分表_直营云产品", "D313-5_自有移动云产品支撑费结算单-云能力中心_YYYYMM_分表_直营云产品"),
    STL_10203("10203", "BL", "D313-5_自有移动云产品支撑费结算单-云能力中心(TAXRATE%)_YYYYMM_分表_合营云产品", "D313-5_自有移动云产品支撑费结算单-云能力中心_YYYYMM_分表_合营云产品"),
    STL_10204("10204", "BL", "Z002_内部单位使用云能力中心产品支撑费结算单(TAXRATE%)_YYYYMM", "Z002_内部单位使用云能力中心产品支撑费结算单_YYYYMM"),
    STL_10205("10205", "BL", "Z002_内部单位使用云能力中心产品支撑费结算单(TAXRATE%)_YYYYMM_分表", "Z002_内部单位使用云能力中心产品支撑费结算单_YYYYMM_分表"),

    STL_10207("10207", "BL", "双跨专线结算补充报表（SRv6-VPN智能专线）ADJUST-总表", "双跨专线结算补充报表（SRv6-VPN智能专线）ADJUST-总表"),
    STL_10208("10208", "BL", "双跨专线结算补充报表（SRv6-VPN智能专线）ADJUST-发起方分表", "双跨专线结算补充报表（SRv6-VPN智能专线）ADJUST-发起方分表"),
    STL_10209("10209", "BL", "双跨专线结算补充报表（SRv6-VPN智能专线）ADJUST-落地方分表", "双跨专线结算补充报表（SRv6-VPN智能专线）ADJUST-落地方分表"),
    
    STL_10001_x("10001_x", "BL", "应收收入ADJUST(TAXRATE%税率)_ENTITY_YYYYMM_销预估", 					"应收收入总表ADJUST_销预估"),
    STL_10007_x("10007_x", "BL", "D202_政企分公司主办集团客户业务应收结算单ADJUST(TAXRATE%)_YYYYMM_销预估", 		"政企分公司主办集团客户业务结算单应收(总表)ADJUST_销预估"),
    STL_10021_x("10021_x", "BL", "D208_省公司主办集团客户业务应收结算单ADJUST(TAXRATE%)_YYYYMM_销预估", 			"省公司主办集团客户业务结算单应收ADJUST_销预估"),
    STL_10034_x("10034_x", "AR", "D232_中国移动车务通业务结算单ADJUST_YYYYMM_销预估", 							"车务通业务结算单ADJUST_销预估"),
    STL_10001_y("10001_y", "BL", "应收收入ADJUST(TAXRATE%税率)_ENTITY_YYYYMM_预估", 					"应收收入总表ADJUST_预估"),
    STL_10007_y("10007_y", "BL", "D202_政企分公司主办集团客户业务应收结算单ADJUST(TAXRATE%)_YYYYMM_预估", 		"政企分公司主办集团客户业务结算单应收(总表)ADJUST_预估"),
    STL_10021_y("10021_y", "BL", "D208_省公司主办集团客户业务应收结算单ADJUST(TAXRATE%)_YYYYMM_预估", 			"省公司主办集团客户业务结算单应收ADJUST_预估"),
    STL_10034_y("10034_y", "AR", "D232_中国移动车务通业务结算单ADJUST_YYYYMM_预估", 							"车务通业务结算单ADJUST_预估"),

    STL_10177("10177","BL","H301_政企条线产品支撑费结算单-总部结算模式_YYYYMM","H301_政企条线产品支撑费结算单-总部结算模式_YYYYMM"),
    STL_10206("10206","BL","H301_政企条线产品支撑费结算单-总部结算模式_考核调整_YYYYMM","H301_政企条线产品支撑费结算单-总部结算模式_考核调整_YYYYMM"),

    // 暂估/销暂估单
    STL_10001_Z("10001_z", "BL", "应收收入(TAXRATE%税率)_ENTITYADJUST暂估_YYYYMM", ""),
    STL_10001_XZ("10001_xz", "BL", "应收收入(TAXRATE%税率)_ENTITYADJUST销暂估_YYYYMM", ""),
    STL_10002_Z("10002_z", "AR", "实收收入ADJUST(TAXRATE%税率)_ENTITY_暂估_YYYYMM", ""),
    STL_10002_XZ("10002_xz", "AR", "实收收入ADJUST(TAXRATE%税率)_ENTITY_销暂估_YYYYMM", ""),
    STL_10007_Z("10007_z", "BL", "D202_政企分公司主办集团客户业务应收结算单(TAXRATE%)ADJUST暂估_YYYYMM", ""),
    STL_10007_XZ("10007_xz", "BL", "D202_政企分公司主办集团客户业务应收结算单(TAXRATE%)ADJUST销暂估_YYYYMM", ""),
    STL_10008_Z("10008_z", "AR", "D202_政企分公司主办集团客户业务实收结算单TAXRATE%)ADJUST_暂估_YYYYMM", ""),
    STL_10008_XZ("10008_xz", "AR", "D202_政企分公司主办集团客户业务实收结算单TAXRATE%)ADJUST_销暂估_YYYYMM", ""),
    STL_10009_Z("10009_z", "BL", "D202_应收结算单ADJUST(TAXRATE%)_暂估_YYYYMM_特殊分表", ""),
    STL_10009_XZ("10009_xz", "BL", "D202_应收结算单ADJUST(TAXRATE%)_销暂估_YYYYMM_特殊分表", ""),
    STL_10010_Z("10010_z", "AR", "D202_实收结算单ADJUST(TAXRATE%)_暂估_YYYYMM_特殊分表", ""),
    STL_10010_XZ("10010_xz", "AR", "D202_实收结算单ADJUST(TAXRATE%)_销暂估_YYYYMM_特殊分表", ""),
    STL_10021_Z("10021_z", "BL", "D208_省公司主办集团客户业务应收结算单(TAXRATE%)ADJUST暂估_YYYYMM", ""),
    STL_10021_XZ("10021_xz", "BL", "D208_省公司主办集团客户业务应收结算单(TAXRATE%)ADJUST销暂估_YYYYMM", ""),
    STL_10022_Z("10022_z", "AR", "D208_省公司主办集团客户业务实收结算单ADJUST(TAXRATE%)_暂估_YYYYMM", ""),
    STL_10022_XZ("10022_xz", "AR", "D208_省公司主办集团客户业务实收结算单ADJUST(TAXRATE%)_销暂估_YYYYMM", ""),
    STL_10019_Z("10019_z", "BL", "D208_省间应收结算单ADJUST_暂估_YYYYMM_特殊分表", ""),
    STL_10019_XZ("10019_xz", "BL", "D208_省间应收结算单ADJUST_销暂估_YYYYMM_特殊分表", ""),
    STL_10020_Z("10020_z", "AR", "D208_省间实收结算单ADJUST_暂估_YYYYMM_特殊分表", ""),
    STL_10020_XZ("10020_xz", "AR", "D208_省间实收结算单ADJUST_销暂估_YYYYMM_特殊分表", ""),
    STL_10034_Z("10034_z", "AR", "D232_车务通业务结算单ADJUST_暂估_YYYYMM", ""),
    STL_10034_XZ("10034_xz", "AR", "D232_车务通业务结算单ADJUST_销暂估_YYYYMM", ""),



    // 202306 第二批 暂估/销暂估单
    STL_10034_Z_2("10034_z_2", "BL", "D232_中国移动车务通业务结算单ADJUST_暂估_YYYYMM", ""),
    STL_10034_XZ_2("10034_xz_2", "BL", "D232_中国移动车务通业务结算单ADJUST_销暂估_YYYYMM", ""),
    STL_10110_Z("10110_z", "BL", "D313_政企条线政企事业部产品支撑费结算单(TAXRATE%)_暂估_YYYYMM", ""),
    STL_10110_XZ("10110_xz", "BL", "D313_政企条线政企事业部产品支撑费结算单(TAXRATE%)_销暂估_YYYYMM", ""),
    STL_10101_Z("10101_z", "BL", "D313-1_政企条线云能力中心产品支撑费结算单ADJALL(TAXRATE%)_暂估_YYYYMM", ""),
    STL_10101_XZ("10101_xz", "BL", "D313-1_政企条线云能力中心产品支撑费结算单ADJALL(TAXRATE%)_销暂估_YYYYMM", ""),
    STL_10107_Z("10107_z", "BL", "D313-2_政企条线成研院产品支撑费结算单(TAXRATE%)_暂估_YYYYMM", ""),
    STL_10107_XZ("10107_xz", "BL", "D313-2_政企条线成研院产品支撑费结算单(TAXRATE%)_销暂估_YYYYMM", ""),
    STL_10106_Z("10106_z", "BL", "D313-3_政企条线上研院产品支撑费结算单(TAXRATE%)_暂估_YYYYMM", ""),
    STL_10106_XZ("10106_xz", "BL", "D313-3_政企条线上研院产品支撑费结算单(TAXRATE%)_销暂估_YYYYMM", ""),
    STL_10109_Z("10109_z", "BL", "D313-4_政企条线雄研院产品支撑费结算单(TAXRATE%)_暂估_YYYYMM", ""),
    STL_10109_XZ("10109_xz", "BL", "D313-4_政企条线雄研院产品支撑费结算单(TAXRATE%)_销暂估_YYYYMM", ""),
    STL_10129_Z("10129_z", "BL", "D313-6_政企条线集成公司产品支撑费结算单(TAXRATE%)_暂估_YYYYMM", ""),
    STL_10129_XZ("10129_xz", "BL", "D313-6_政企条线集成公司产品支撑费结算单(TAXRATE%)_销暂估_YYYYMM", ""),
    STL_10111_Z("10111_z", "BL", "D313-7_政企条线咪咕公司产品支撑费结算单(TAXRATE%)_暂估_YYYYMM", ""),
    STL_10111_XZ("10111_xz", "BL", "D313-7_政企条线咪咕公司产品支撑费结算单(TAXRATE%)_销暂估_YYYYMM", ""),
    STL_10112_Z("10112_z", "BL", "D313-8_政企条线互联网公司产品支撑费结算单(TAXRATE%)_暂估_YYYYMM", ""),
    STL_10112_XZ("10112_xz", "BL", "D313-8_政企条线互联网公司产品支撑费结算单(TAXRATE%)_销暂估_YYYYMM", ""),
    STL_10124_Z("10124_z", "BL", "D313-9-省专结算报表-位置基地_暂估_YYYYMM", ""),
    STL_10124_XZ("10124_xz", "BL", "D313-9-省专结算报表-位置基地_销暂估_YYYYMM", ""),
    STL_10128_Z("10128_z", "BL", "D313-10_政企条线卓望公司产品支撑费结算单(TAXRATE%)_暂估_YYYYMM", ""),
    STL_10128_XZ("10128_xz", "BL", "D313-10_政企条线卓望公司产品支撑费结算单(TAXRATE%)_销暂估_YYYYMM", ""),
    STL_10145_Z("10145_z", "BL", "D313-11_政企条线金科公司产品支撑费结算单(TAXRATE%)_暂估_YYYYMM", ""),
    STL_10145_XZ("10145_xz", "BL", "D313-11_政企条线金科公司产品支撑费结算单(TAXRATE%)_销暂估_YYYYMM", ""),
    STL_10147_Z("10147_z", "BL", "D313-12_政企条线物联网公司产品支撑费结算单(TAXRATE%)_暂估_YYYYMM", ""),
    STL_10147_XZ("10147_xz", "BL", "D313-12_政企条线物联网公司产品支撑费结算单(TAXRATE%)_销暂估_YYYYMM", ""),
    STL_10116_Z("10116_z", "BL", "D335_政企条线政企事业部产品合作分成结算单(TAXRATE%)_暂估_YYYYMM", ""),
    STL_10116_XZ("10116_xz", "BL", "D335_政企条线政企事业部产品合作分成结算单(TAXRATE%)_销暂估_YYYYMM", ""),
    STL_10131_Z("10131_z", "BL", "D335-1_政企条线云能力中心产品合作分成结算单(TAXRATE%)_暂估_YYYYMM", ""),
    STL_10131_XZ("10131_xz", "BL", "D335-1_政企条线云能力中心产品合作分成结算单(TAXRATE%)_销暂估_YYYYMM", ""),
    STL_10108_Z("10108_z", "BL", "D335-2-政企合作分成业务-成研_暂估_YYYYMM", ""),
    STL_10108_XZ("10108_xz", "BL", "D335-2-政企合作分成业务-成研_销暂估_YYYYMM", ""),
    STL_10123_Z("10123_z", "BL", "D335-5_政企条线互联网公司产品合作分成结算单(TAXRATE%)_暂估_YYYYMM", ""),
    STL_10123_XZ("10123_xz", "BL", "D335-5_政企条线互联网公司产品合作分成结算单(TAXRATE%)_销暂估_YYYYMM", ""),
    STL_10136_Z("10136_z", "BL", "D335-6_政企条线终端公司产品结算单(TAXRATE%)_暂估_YYYYMM", ""),
    STL_10136_XZ("10136_xz", "BL", "D335-6_政企条线终端公司产品结算单(TAXRATE%)_销暂估_YYYYMM", ""),
    STL_10120_Z("10120_z", "BL", "D340-政企捆绑销售业务结算单-咪咕_暂估_YYYYMM", ""),
    STL_10120_XZ("10120_xz", "BL", "D340-政企捆绑销售业务结算单-咪咕_销暂估_YYYYMM", ""),
    STL_10102_Z("10102_z", "BL", "D332_集团客户业务代收代付-云能力中心(TAXRATE%)_暂估_YYYYMM", ""),
    STL_10102_XZ("10102_xz", "BL", "D332_集团客户业务代收代付-云能力中心(TAXRATE%)_销暂估_YYYYMM", ""),
    // BIL-NB-202304-01-新增第二批暂估单文件
    STL_10001_Z_2("10001_z_2", "BL", "应收收入ADJUST(TAXRATE%税率)_ENTITY_暂估_YYYYMM", ""), //下线，保留10001_z
    STL_10001_XZ_2("10001_xz_2", "BL", "应收收入ADJUST(TAXRATE%税率)_ENTITY_销暂估_YYYYMM", ""),//下线，保留10001_xz
    STL_10103_Z("10103_z", "BL", "移动云合作伙伴结算单_暂估_YYYYMM", ""),
    STL_10103_XZ("10103_xz", "BL", "移动云合作伙伴结算单_销暂估_YYYYMM", ""),
    STL_10104_Z("10104_z", "BL", "华为合营云合作伙伴结算报表_暂估_YYYYMM", ""),
    STL_10104_XZ("10104_xz", "BL", "华为合营云合作伙伴结算报表_销暂估_YYYYMM", ""),
    STL_10114_Z("10114_z", "BL", "政企事业部与合作伙伴DICT增值服务产品结算单(TAXRATE%)_暂估_YYYYMM", ""),
    STL_10114_XZ("10114_xz", "BL", "政企事业部与合作伙伴DICT增值服务产品结算单(TAXRATE%)_销暂估_YYYYMM", ""),
    STL_10115_Z("10115_z", "BL", "阿里云转售业务结算单_暂估_YYYYMM", ""),
    STL_10115_XZ("10115_xz", "BL", "阿里云转售业务结算单_销暂估_YYYYMM", ""),
    STL_10117_Z("10117_z", "BL", "公播音乐合作伙伴产品业务提供商结算收入报表_暂估_YYYYMM", ""),
    STL_10117_XZ("10117_xz", "BL", "公播音乐合作伙伴产品业务提供商结算收入报表_销暂估_YYYYMM", ""),
    STL_10118_Z("10118_z", "BL", "和对讲合作伙伴产品业务提供商结算收入报表_暂估_YYYYMM", ""),
    STL_10118_XZ("10118_xz", "BL", "和对讲合作伙伴产品业务提供商结算收入报表_销暂估_YYYYMM", ""),
    STL_10119_Z("10119_z", "BL", "千里眼合作伙伴产品业务提供商结算收入报表_暂估_YYYYMM", ""),
    STL_10119_XZ("10119_xz", "BL", "千里眼合作伙伴产品业务提供商结算收入报表_销暂估_YYYYMM", ""),
    STL_10121_Z("10121_z", "BL", "移动办公-联想权益合作伙伴产品业务提供商结算收入报表_暂估_YYYYMM", ""),
    STL_10121_XZ("10121_xz", "BL", "移动办公-联想权益合作伙伴产品业务提供商结算收入报表_销暂估_YYYYMM", ""),
    STL_10122_Z("10122_z", "BL", "移动办公-企业应用合作伙伴产品业务提供商结算收入报表_暂估_YYYYMM", ""),
    STL_10122_XZ("10122_xz", "BL", "移动办公-企业应用合作伙伴产品业务提供商结算收入报表_销暂估_YYYYMM", ""),
    STL_10125_Z("10125_z", "BL", "移动办公-i笛云合作伙伴产品业务提供商结算收入报表_暂估_YYYYMM", ""),
    STL_10125_XZ("10125_xz", "BL", "移动办公-i笛云合作伙伴产品业务提供商结算收入报表_销暂估_YYYYMM", ""),
    STL_10126_Z("10126_z", "BL", "移动办公-口袋扫描仪合作伙伴产品业务提供商结算收入报表_暂估_YYYYMM", ""),
    STL_10126_XZ("10126_xz", "BL", "移动办公-口袋扫描仪合作伙伴产品业务提供商结算收入报表_销暂估_YYYYMM", ""),
    STL_10127_Z("10127_z", "BL", "移动办公-公文管理、督办管理、发票识别验真合作伙伴产品业务提供商结算收入报表_暂估_YYYYMM", ""),
    STL_10127_XZ("10127_xz", "BL", "移动办公-公文管理、督办管理、发票识别验真合作伙伴产品业务提供商结算收入报表_销暂估_YYYYMM", ""),
    STL_10130_Z("10130_z", "BL", "移动办公-WPS合作伙伴产品业务提供商结算收入报表_暂估_YYYYMM", ""),
    STL_10130_XZ("10130_xz", "BL", "移动办公-WPS合作伙伴产品业务提供商结算收入报表_销暂估_YYYYMM", ""),
    STL_10132_Z("10132_z", "BL", "移动办公-福昕权益合作伙伴产品业务提供商结算收入报表_暂估_YYYYMM", ""),
    STL_10132_XZ("10132_xz", "BL", "移动办公-福昕权益合作伙伴产品业务提供商结算收入报表_销暂估_YYYYMM", ""),
    STL_10133_Z("10133_z", "BL", "移动办公-有道云笔记合作伙伴产品业务提供商结算收入报表_暂估_YYYYMM", ""),
    STL_10133_XZ("10133_xz", "BL", "移动办公-有道云笔记合作伙伴产品业务提供商结算收入报表_销暂估_YYYYMM", ""),
    STL_10134_Z("10134_z", "BL", "移动办公-ProcessOn合作伙伴产品业务提供商结算收入报表_暂估_YYYYMM", ""),
    STL_10134_XZ("10134_xz", "BL", "移动办公-ProcessOn合作伙伴产品业务提供商结算收入报表_销暂估_YYYYMM", ""),
    STL_10135_Z("10135_z", "BL", "移动办公-Microsoft 365合作伙伴产品业务提供商结算收入报表ADJUST_暂估_YYYYMM", ""),
    STL_10135_XZ("10135_xz", "BL", "移动办公-Microsoft 365合作伙伴产品业务提供商结算收入报表ADJUST_销暂估_YYYYMM", ""),
    STL_10148_Z("10148_z", "BL", "中移物联网公司与合作伙伴DICT增值服务产品结算单_暂估_YYYYMM", ""),
    STL_10148_XZ("10148_xz", "BL", "中移物联网公司与合作伙伴DICT增值服务产品结算单_销暂估_YYYYMM", ""),
	STL_10159_Z("10159_z", "BL", "D313-8-省专结算报表-阅信业务分表(TAXRATE%)_暂估_YYYYMM", ""),
    STL_10159_XZ("10159_xz", "BL", "D313-8-省专结算报表-阅信业务分表(TAXRATE%)_销暂估_YYYYMM", ""),
    // 新增暂估单D313-5
    STL_10156_Z("10156_z", "BL", "D313-5_自有移动云产品支撑费结算单-云能力中心(TAXRATE%)_暂估_YYYYMM", ""),
    STL_10156_XZ("10156_xz", "BL", "D313-5_自有移动云产品支撑费结算单-云能力中心(TAXRATE%)_销暂估_YYYYMM", ""),
    // D208-1 idc协同营销
    STL_10161_Z("10161_z", "BL", "D208-1_省公司主办集团客户业务结算单（IDC协同营销）ADJUST(TAXRATE%)_暂估_YYYYMM", ""),
    STL_10161_XZ("10161_xz", "BL", "D208-1_省公司主办集团客户业务结算单（IDC协同营销）ADJUST(TAXRATE%)_销暂估_YYYYMM", ""),
    STL_10163_Z("10163_z", "BL", "D313_政企条线专业公司标准产品DICT服务包结算单(TAXRATE%)_暂估_YYYYMM", ""),
    STL_10163_XZ("10163_xz", "BL", "D313_政企条线专业公司标准产品DICT服务包结算单(TAXRATE%)_销暂估_YYYYMM", ""),
    STL_10166_Z("10166_z", "BL", "移动办公-数字人名片产品业务提供商结算收入报表_暂估_YYYYMM", ""),
    STL_10166_XZ("10166_xz", "BL", "移动办公-数字人名片产品业务提供商结算收入报表_销暂估_YYYYMM", ""),
    STL_10168_Z("10168_z", "BL", "D208-1_省公司主办集团客户业务结算单（IDC协同营销）ADJUST_特殊分表_暂估_YYYYMM", ""),
    STL_10168_XZ("10168_xz", "BL", "D208-1_省公司主办集团客户业务结算单（IDC协同营销）ADJUST_特殊分表_销暂估_YYYYMM", ""),
    STL_10149_Z("10149_z", "BL", "D313-13_政企条线设计院产品支撑费结算单ADJALL(TAXRATE%)_暂估_YYYYMM", ""),
    STL_10149_XZ("10149_xz", "BL", "D313-13_政企条线设计院产品支撑费结算单ADJALL(TAXRATE%)_销暂估_YYYYMM", ""),
    STL_10177_Z("10177_z", "BL", "H301_政企条线产品支撑费结算单-总部结算模式ADJUST_暂估_YYYYMM", ""),
    STL_10177_XZ("10177_xz", "BL", "H301_政企条线产品支撑费结算单-总部结算模式ADJUST_销暂估_YYYYMM", ""),
    STL_10180_Z("10180_z", "BL", "D313-9_政企条线中移智行产品支撑费结算单ADJALL(TAXRATE%)_暂估_YYYYMM", ""),
    STL_10180_XZ("10180_xz", "BL", "D313-9_政企条线中移智行产品支撑费结算单ADJALL(TAXRATE%)_销暂估_YYYYMM", ""),
    STL_10197_Z("10197_z", "BL", "D335-7_政企条线呼池智算产品合作分成结算单ADJALL(TAXRATE%)_暂估_YYYYMM", ""),
    STL_10197_XZ("10197_xz", "BL", "D335-7_政企条线呼池智算产品合作分成结算单ADJALL(TAXRATE%)_销暂估_YYYYMM", ""),
    STL_10199_Z("10199_z", "BL", "Z003_政企条线呼池智算产品支撑费结算单ADJALL(TAXRATE%)_暂估_YYYYMM", ""),
    STL_10199_XZ("10199_xz", "BL", "Z003_政企条线呼池智算产品支撑费结算单ADJALL(TAXRATE%)_销暂估_YYYYMM", ""),
    STL_10201_Z("10201_z", "BL", "D313-14_政企条线信息技术公司产品支撑费结算单ADJALL(TAXRATE%)_暂估_YYYYMM", ""),
    STL_10201_XZ("10201_xz", "BL", "D313-14_政企条线信息技术公司产品支撑费结算单ADJALL(TAXRATE%)_销暂估_YYYYMM", "");

    /**
     * @Fields raqKey : 报表对应KEY值
     */
    private String raqKey;

    private String settMode;
    /**
     * @Fields raqName : 报表对应导出文件名
     */
    private String raqName;
    /**
     * @Fields raqZipName : 报表对应下载压缩包文件名
     */
    private String raqZipName;

    public String getRaqKey() {
        return raqKey;
    }

    public String getRaqName() {
        return raqName;
    }

    public String getRaqZipName() {
        return raqZipName;
    }

    public String getSettMode() {
        return settMode;
    }

    ConstantsEnum(String raqKey, String settMode, String raqName, String raqZipName) {
        this.raqKey = raqKey;
        this.settMode = settMode;
        this.raqName = raqName;
        this.raqZipName = raqZipName;
    }

    /**
     * @Title: isRaqKey
     * @Description: 返回对应枚举
     * @param @param key
     * @param @return    参数
     * @return ConstantsEnum    返回类型
     * @throws
     */
    public static ConstantsEnum isRaqKey(String key){
        for(ConstantsEnum ce : ConstantsEnum.values()){
            if(key.equals(ce.getRaqKey())){
                return ce;
            }
        }
        return null;
    }

}
